using UnityEngine;
using System.Linq;
using System.Collections.Generic;

/// <summary>
/// Simple leaderboard display using Debug.Log output (no UI dependencies)
/// Perfect for training monitoring without UI setup
/// </summary>
public class SimpleLeaderboardUI : MonoBehaviour
{
    [Header("🏆 Display Settings")]
    public bool enableConsoleOutput = true;
    public bool enableOnScreenDisplay = false;

    [Header("⚙️ Settings")]
    public float refreshRate = 5f;
    public int maxDisplayedAgents = 10;
    public bool sortByScore = true;

    [Header("📊 Display Options")]
    public bool showDetailedStats = true;
    public bool showTeamSummary = true;

    private string lastLeaderboardOutput = "";

    void Start()
    {
        Debug.Log("🏆 SquadMate AI Leaderboard Started");
        InvokeRepeating(nameof(UpdateLeaderboard), 2f, refreshRate);
    }

    void UpdateLeaderboard()
    {
        if (!enableConsoleOutput) return;

        // Find all agents with stats
        AgentStats[] allAgents = FindObjectsOfType<AgentStats>();

        if (allAgents.Length == 0)
        {
            Debug.Log("📊 No agents found - Waiting for training to start...");
            return;
        }

        // Sort agents
        var sortedAgents = SortAgents(allAgents);

        // Generate leaderboard output
        string output = GenerateLeaderboardOutput(sortedAgents, allAgents);

        // Only log if output changed (to avoid spam)
        if (output != lastLeaderboardOutput)
        {
            Debug.Log(output);
            lastLeaderboardOutput = output;
        }
    }

    AgentStats[] SortAgents(AgentStats[] agents)
    {
        if (sortByScore)
        {
            return agents.OrderByDescending(a => a.GetScore())
                        .ThenByDescending(a => a.GetKDR())
                        .ToArray();
        }
        else
        {
            return agents.OrderByDescending(a => a.kills)
                        .ThenByDescending(a => a.GetKDR())
                        .ToArray();
        }
    }

    string GenerateLeaderboardOutput(AgentStats[] sortedAgents, AgentStats[] allAgents)
    {
        string output = "\n🏆 ===== SQUADMATE AI LEADERBOARD ===== 🏆\n";

        // Round summary
        int aliveCount = allAgents.Count(a => a.IsAlive);
        int totalKills = allAgents.Sum(a => a.kills);
        int totalRevives = allAgents.Sum(a => a.revives);
        float avgAccuracy = allAgents.Length > 0 ? allAgents.Average(a => a.accuracy) : 0f;

        output += $"📊 ROUND STATS: {aliveCount}/{allAgents.Length} alive | ";
        output += $"Total Kills: {totalKills} | Total Revives: {totalRevives} | ";
        output += $"Avg Accuracy: {avgAccuracy:F1}%\n\n";

        // Team summary
        if (showTeamSummary)
        {
            var teamA = allAgents.Where(a => a.teamName.ToLower().Contains("a")).ToArray();
            var teamB = allAgents.Where(a => a.teamName.ToLower().Contains("b")).ToArray();

            if (teamA.Length > 0 && teamB.Length > 0)
            {
                int teamAKills = teamA.Sum(a => a.kills);
                int teamBKills = teamB.Sum(a => a.kills);
                int teamAAlive = teamA.Count(a => a.IsAlive);
                int teamBAlive = teamB.Count(a => a.IsAlive);

                output += $"🔵 TEAM A: {teamAAlive}/{teamA.Length} alive, {teamAKills} kills\n";
                output += $"🔴 TEAM B: {teamBAlive}/{teamB.Length} alive, {teamBKills} kills\n\n";
            }
        }

        // Individual rankings
        output += "🎯 TOP PERFORMERS:\n";
        int displayCount = Mathf.Min(maxDisplayedAgents, sortedAgents.Length);

        for (int i = 0; i < displayCount; i++)
        {
            var agent = sortedAgents[i];
            string status = agent.IsAlive ? "🟢" : "💀";
            string teamIcon = GetTeamIcon(agent.teamName);

            if (showDetailedStats)
            {
                output += $"{i + 1}. {teamIcon} {agent.agentName} ({agent.role}) {status}\n";
                output += $"   Score: {agent.GetScore()} | K/D: {agent.GetFormattedKDR()} | ";
                output += $"Kills: {agent.kills} | Revives: {agent.revives} | ";
                output += $"Zones: {agent.zonesCaptures} | Acc: {agent.GetFormattedAccuracy()}\n";
            }
            else
            {
                output += $"{i + 1}. {teamIcon} {agent.agentName} - Score: {agent.GetScore()} | ";
                output += $"K/D: {agent.GetFormattedKDR()} | {status}\n";
            }
        }

        output += "\n" + new string('=', 50);
        return output;
    }

    string GetTeamIcon(string teamName)
    {
        if (teamName.ToLower().Contains("a") || teamName.ToLower().Contains("blue"))
            return "🔵";
        else if (teamName.ToLower().Contains("b") || teamName.ToLower().Contains("red"))
            return "🔴";
        else
            return "⚪";
    }

    #region Public Methods
    [ContextMenu("Toggle Sort Mode")]
    public void ToggleSortMode()
    {
        sortByScore = !sortByScore;
        string mode = sortByScore ? "Score" : "Kills";
        Debug.Log($"📊 Leaderboard sort mode changed to: {mode}");
        UpdateLeaderboard();
    }

    [ContextMenu("Show Current Leaderboard")]
    public void ShowCurrentLeaderboard()
    {
        UpdateLeaderboard();
    }

    [ContextMenu("Export to CSV")]
    public void ExportToCSV()
    {
        AgentStats[] agents = FindObjectsOfType<AgentStats>();
        if (agents.Length == 0)
        {
            Debug.LogWarning("No agents found to export");
            return;
        }

        string csv = "Agent,Team,Role,Score,Kills,Deaths,Assists,KDR,Revives,Zones,Accuracy,Status\n";

        foreach (var agent in SortAgents(agents))
        {
            csv += $"{agent.agentName},{agent.teamName},{agent.role},{agent.GetScore()}," +
                   $"{agent.kills},{agent.deaths},{agent.assists},{agent.GetKDR():F2}," +
                   $"{agent.revives},{agent.zonesCaptures},{agent.accuracy:F1}," +
                   $"{(agent.IsAlive ? "Alive" : "KIA")}\n";
        }

        string filename = $"leaderboard_{System.DateTime.Now:yyyyMMdd_HHmmss}.csv";
        System.IO.File.WriteAllText(filename, csv);
        Debug.Log($"📊 Leaderboard exported to {filename}");
    }

    public void SetRefreshRate(float newRate)
    {
        refreshRate = Mathf.Clamp(newRate, 1f, 30f);
        CancelInvoke(nameof(UpdateLeaderboard));
        InvokeRepeating(nameof(UpdateLeaderboard), 2f, refreshRate);
        Debug.Log($"📊 Leaderboard refresh rate set to {refreshRate} seconds");
    }

    public void ToggleConsoleOutput()
    {
        enableConsoleOutput = !enableConsoleOutput;
        Debug.Log($"📊 Console output {(enableConsoleOutput ? "enabled" : "disabled")}");
    }
    #endregion
}
