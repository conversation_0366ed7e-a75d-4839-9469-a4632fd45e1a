using UnityEngine;
using UnityEngine.UI;
using System.Linq;
using System.Collections.Generic;

/// <summary>
/// Simple leaderboard UI using standard Unity Text components (no TextMeshPro required)
/// </summary>
public class SimpleLeaderboardUI : MonoBehaviour
{
    [Header("🏆 UI References")]
    public Transform leaderboardContainer;
    public Text titleText;
    public Text roundInfoText;

    [Header("⚙️ Settings")]
    public float refreshRate = 2f;
    public int maxDisplayedAgents = 10;
    public bool sortByScore = true;

    [Header("🎨 Team Colors")]
    public Color teamAColor = Color.blue;
    public Color teamBColor = Color.red;

    void Start()
    {
        if (titleText != null)
        {
            titleText.text = "SquadMate AI Leaderboard";
        }

        InvokeRepeating(nameof(UpdateLeaderboard), 1f, refreshRate);
    }

    void UpdateLeaderboard()
    {
        // Clear existing rows
        foreach (Transform child in leaderboardContainer)
        {
            if (child != leaderboardContainer)
            {
                Destroy(child.gameObject);
            }
        }

        // Find all agents with stats
        AgentStats[] allAgents = FindObjectsOfType<AgentStats>();

        if (allAgents.Length == 0)
        {
            CreateNoDataRow();
            return;
        }

        // Sort agents
        var sortedAgents = SortAgents(allAgents);

        // Update round info
        UpdateRoundInfo(allAgents);

        // Create simple text rows
        int displayCount = Mathf.Min(maxDisplayedAgents, sortedAgents.Length);
        for (int i = 0; i < displayCount; i++)
        {
            CreateSimpleAgentRow(sortedAgents[i], i + 1);
        }
    }

    AgentStats[] SortAgents(AgentStats[] agents)
    {
        if (sortByScore)
        {
            return agents.OrderByDescending(a => a.GetScore())
                        .ThenByDescending(a => a.GetKDR())
                        .ToArray();
        }
        else
        {
            return agents.OrderByDescending(a => a.kills)
                        .ThenByDescending(a => a.GetKDR())
                        .ToArray();
        }
    }

    void CreateSimpleAgentRow(AgentStats agent, int rank)
    {
        // Create a simple text row
        GameObject row = new GameObject($"Row_{agent.agentName}");
        row.transform.SetParent(leaderboardContainer);

        Text rowText = row.AddComponent<Text>();
        rowText.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
        rowText.fontSize = 14;
        rowText.color = GetTeamColor(agent.teamName);

        string status = agent.IsAlive ? "ALIVE" : "KIA";
        string rowContent = $"{rank}. {agent.agentName} | {agent.role} | " +
                           $"Score: {agent.GetScore()} | K/D: {agent.GetFormattedKDR()} | " +
                           $"Kills: {agent.kills} | Revives: {agent.revives} | " +
                           $"Zones: {agent.zonesCaptures} | Acc: {agent.GetFormattedAccuracy()} | {status}";

        rowText.text = rowContent;

        // Set layout
        RectTransform rectTransform = row.GetComponent<RectTransform>();
        rectTransform.anchorMin = new Vector2(0, 1);
        rectTransform.anchorMax = new Vector2(1, 1);
        rectTransform.pivot = new Vector2(0, 1);
        rectTransform.sizeDelta = new Vector2(0, 20);
        rectTransform.anchoredPosition = new Vector2(0, -rank * 25);
    }

    Color GetTeamColor(string teamName)
    {
        if (teamName.ToLower().Contains("a") || teamName.ToLower().Contains("blue"))
            return teamAColor;
        else if (teamName.ToLower().Contains("b") || teamName.ToLower().Contains("red"))
            return teamBColor;
        else
            return Color.white;
    }

    void CreateNoDataRow()
    {
        GameObject row = new GameObject("NoDataRow");
        row.transform.SetParent(leaderboardContainer);

        Text rowText = row.AddComponent<Text>();
        rowText.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
        rowText.fontSize = 16;
        rowText.color = Color.yellow;
        rowText.text = "No agents found - Waiting for training to start...";

        RectTransform rectTransform = row.GetComponent<RectTransform>();
        rectTransform.anchorMin = new Vector2(0, 1);
        rectTransform.anchorMax = new Vector2(1, 1);
        rectTransform.pivot = new Vector2(0, 1);
        rectTransform.sizeDelta = new Vector2(0, 30);
        rectTransform.anchoredPosition = new Vector2(0, 0);
    }

    void UpdateRoundInfo(AgentStats[] agents)
    {
        if (roundInfoText == null) return;

        int aliveCount = agents.Count(a => a.IsAlive);
        int totalKills = agents.Sum(a => a.kills);
        int totalRevives = agents.Sum(a => a.revives);
        float avgAccuracy = agents.Length > 0 ? agents.Average(a => a.accuracy) : 0f;

        string info = $"Agents: {aliveCount}/{agents.Length} alive | " +
                     $"Total Kills: {totalKills} | " +
                     $"Total Revives: {totalRevives} | " +
                     $"Avg Accuracy: {avgAccuracy:F1}%";

        roundInfoText.text = info;
    }

    #region Public Methods
    public void ToggleSortMode()
    {
        sortByScore = !sortByScore;
        UpdateLeaderboard();
    }

    public void SetRefreshRate(float newRate)
    {
        refreshRate = Mathf.Clamp(newRate, 0.5f, 10f);
        CancelInvoke(nameof(UpdateLeaderboard));
        InvokeRepeating(nameof(UpdateLeaderboard), 1f, refreshRate);
    }

    public void ExportToCSV()
    {
        AgentStats[] agents = FindObjectsOfType<AgentStats>();
        if (agents.Length == 0) return;

        string csv = "Agent,Team,Role,Score,Kills,Deaths,Assists,KDR,Revives,Zones,Accuracy,Status\n";

        foreach (var agent in SortAgents(agents))
        {
            csv += $"{agent.agentName},{agent.teamName},{agent.role},{agent.GetScore()}," +
                   $"{agent.kills},{agent.deaths},{agent.assists},{agent.GetKDR():F2}," +
                   $"{agent.revives},{agent.zonesCaptures},{agent.accuracy:F1}," +
                   $"{(agent.IsAlive ? "Alive" : "KIA")}\n";
        }

        string filename = $"leaderboard_{System.DateTime.Now:yyyyMMdd_HHmmss}.csv";
        System.IO.File.WriteAllText(filename, csv);
        Debug.Log($"Leaderboard exported to {filename}");
    }
    #endregion
}
