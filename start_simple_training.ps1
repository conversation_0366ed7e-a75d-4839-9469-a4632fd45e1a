Write-Host "🚀 Starting Simple PUBG Training..." -ForegroundColor Green
Write-Host ""
Write-Host "This will start ML-Agents training with simplified configuration" -ForegroundColor Yellow
Write-Host "Make sure Unity is running with the Simple PUBG Scene loaded" -ForegroundColor Yellow
Write-Host ""

$continue = Read-Host "Press Enter to continue or Ctrl+C to cancel"

Write-Host "Starting training..." -ForegroundColor Green
try {
    mlagents-learn config/simple_pubg_config.yaml --run-id=simple_pubg_training --force
    Write-Host "Training completed successfully!" -ForegroundColor Green
} catch {
    Write-Host "Training failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Press Enter to exit..."
Read-Host
