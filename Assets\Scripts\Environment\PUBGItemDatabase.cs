using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// PUBG Item Types
/// </summary>
public enum PUBGItemType
{
    Weapon,
    Healing,
    Armor,
    Backpack,
    Attachment,
    Throwable
}

/// <summary>
/// Item Rarity System
/// </summary>
public enum ItemRarity
{
    Common = 1,
    Uncommon = 2,
    Rare = 3,
    Epic = 4,
    Legendary = 5
}

/// <summary>
/// Weapon Categories
/// </summary>
public enum WeaponCategory
{
    AssaultRifle,
    SMG,
    SniperRifle,
    Shotgun,
    Pistol,
    DMR
}

/// <summary>
/// Healing Item Types
/// </summary>
public enum HealingType
{
    Bandage,
    FirstAidKit,
    MedKit,
    EnergyDrink,
    Painkiller,
    AdrenalineSyringe
}

/// <summary>
/// PUBG Item Data Structure
/// </summary>
[System.Serializable]
public class PUBGItem
{
    [Header("📦 Basic Info")]
    public string itemName;
    public string description;
    public PUBGItemType itemType;
    public ItemRarity rarity;
    public Sprite icon;

    [Header("⚖️ Properties")]
    public float weight = 1f;
    public int stackSize = 1;
    public bool isConsumable = false;

    [Header("🔫 Weapon Stats (if weapon)")]
    public WeaponCategory weaponCategory;
    public float damage = 0f;
    public float range = 0f;
    public float fireRate = 0f;
    public int ammoCapacity = 0;
    public string ammoType = "";

    [Header("💊 Healing Stats (if healing)")]
    public HealingType healingType;
    public float healAmount = 0f;
    public float useTime = 0f;
    public float maxHealthPercent = 1f; // Some items can't heal above 75%
    public bool isBoostItem = false; // Energy drinks, painkillers

    [Header("🛡️ Armor Stats (if armor)")]
    public int armorLevel = 0; // 1-3
    public float damageReduction = 0f;
    public bool isHelmet = false;
    public bool isVest = false;

    [Header("🎒 Backpack Stats (if backpack)")]
    public int capacityBonus = 0;

    [Header("🔧 Attachment Stats (if attachment)")]
    public string[] compatibleWeapons;
    public float recoilReduction = 0f;
    public float stabilityBonus = 0f;
    public float rangeBonus = 0f;

    [Header("💣 Throwable Stats (if throwable)")]
    public float throwRange = 30f;
    public float fuseTime = 3f;
    public float explosionRadius = 5f;
    public float explosionDamage = 100f;

    [Header("🎯 AI Priority")]
    public float aiPriority = 1f; // Higher = more important for AI
    public bool isEssential = false; // Critical items like medkits when low health
}

/// <summary>
/// PUBG Item Database - Contains all item definitions
/// </summary>
[CreateAssetMenu(fileName = "PUBGItemDatabase", menuName = "SquadMate/PUBG Item Database")]
public class PUBGItemDatabase : ScriptableObject
{
    [Header("🗃️ Item Collections")]
    public List<PUBGItem> weapons = new List<PUBGItem>();
    public List<PUBGItem> healingItems = new List<PUBGItem>();
    public List<PUBGItem> armorItems = new List<PUBGItem>();
    public List<PUBGItem> backpacks = new List<PUBGItem>();
    public List<PUBGItem> attachments = new List<PUBGItem>();
    public List<PUBGItem> throwables = new List<PUBGItem>();

    [Header("🎲 Spawn Weights")]
    public float weaponSpawnWeight = 35f;
    public float healingSpawnWeight = 25f;
    public float armorSpawnWeight = 15f;
    public float backpackSpawnWeight = 10f;
    public float attachmentSpawnWeight = 10f;
    public float throwableSpawnWeight = 5f;

    public void InitializeDefaultItems()
    {
        CreateDefaultWeapons();
        CreateDefaultHealingItems();
        CreateDefaultArmorItems();
        CreateDefaultBackpacks();
        CreateDefaultAttachments();
        CreateDefaultThrowables();
    }

    void CreateDefaultWeapons()
    {
        weapons.Clear();

        // Assault Rifles
        weapons.Add(CreateWeapon("M416", WeaponCategory.AssaultRifle, ItemRarity.Rare, 43f, 500f, 0.086f, 30, "5.56mm"));
        weapons.Add(CreateWeapon("AKM", WeaponCategory.AssaultRifle, ItemRarity.Uncommon, 49f, 400f, 0.1f, 30, "7.62mm"));
        weapons.Add(CreateWeapon("SCAR-L", WeaponCategory.AssaultRifle, ItemRarity.Rare, 43f, 450f, 0.096f, 30, "5.56mm"));
        weapons.Add(CreateWeapon("Beryl M762", WeaponCategory.AssaultRifle, ItemRarity.Epic, 47f, 450f, 0.092f, 30, "7.62mm"));

        // SMGs
        weapons.Add(CreateWeapon("UMP45", WeaponCategory.SMG, ItemRarity.Common, 39f, 200f, 0.092f, 25, ".45 ACP"));
        weapons.Add(CreateWeapon("Vector", WeaponCategory.SMG, ItemRarity.Rare, 31f, 150f, 0.055f, 13, "9mm"));
        weapons.Add(CreateWeapon("Tommy Gun", WeaponCategory.SMG, ItemRarity.Uncommon, 42f, 180f, 0.086f, 30, ".45 ACP"));

        // Sniper Rifles
        weapons.Add(CreateWeapon("Kar98k", WeaponCategory.SniperRifle, ItemRarity.Epic, 79f, 1000f, 1.9f, 5, "7.62mm"));
        weapons.Add(CreateWeapon("M24", WeaponCategory.SniperRifle, ItemRarity.Legendary, 84f, 1200f, 1.8f, 5, "7.62mm"));
        weapons.Add(CreateWeapon("AWM", WeaponCategory.SniperRifle, ItemRarity.Legendary, 120f, 1500f, 4.1f, 5, ".300 Magnum"));

        // DMRs
        weapons.Add(CreateWeapon("SKS", WeaponCategory.DMR, ItemRarity.Uncommon, 53f, 800f, 0.133f, 10, "7.62mm"));
        weapons.Add(CreateWeapon("Mini 14", WeaponCategory.DMR, ItemRarity.Rare, 46f, 900f, 0.1f, 20, "5.56mm"));

        // Shotguns
        weapons.Add(CreateWeapon("S1897", WeaponCategory.Shotgun, ItemRarity.Common, 26f, 50f, 1.0f, 5, "12 Gauge"));
        weapons.Add(CreateWeapon("S686", WeaponCategory.Shotgun, ItemRarity.Uncommon, 26f, 50f, 0.2f, 2, "12 Gauge"));
        weapons.Add(CreateWeapon("S12K", WeaponCategory.Shotgun, ItemRarity.Rare, 24f, 60f, 0.25f, 5, "12 Gauge"));

        // Pistols
        weapons.Add(CreateWeapon("P1911", WeaponCategory.Pistol, ItemRarity.Common, 35f, 100f, 0.11f, 7, ".45 ACP"));
        weapons.Add(CreateWeapon("P92", WeaponCategory.Pistol, ItemRarity.Common, 29f, 120f, 0.1f, 15, "9mm"));
        weapons.Add(CreateWeapon("R1895", WeaponCategory.Pistol, ItemRarity.Uncommon, 46f, 150f, 0.5f, 7, "7.62mm"));
    }

    PUBGItem CreateWeapon(string name, WeaponCategory category, ItemRarity rarity, float damage, float range, float fireRate, int ammo, string ammoType)
    {
        PUBGItem weapon = new PUBGItem();
        weapon.itemName = name;
        weapon.itemType = PUBGItemType.Weapon;
        weapon.weaponCategory = category;
        weapon.rarity = rarity;
        weapon.damage = damage;
        weapon.range = range;
        weapon.fireRate = fireRate;
        weapon.ammoCapacity = ammo;
        weapon.ammoType = ammoType;
        weapon.weight = 3f + (int)rarity * 0.5f;
        weapon.description = $"{category} - {damage} damage, {range}m range";
        return weapon;
    }

    void CreateDefaultHealingItems()
    {
        healingItems.Clear();

        healingItems.Add(CreateHealingItem("Bandage", HealingType.Bandage, ItemRarity.Common, 10f, 4f, 0.75f, false));
        healingItems.Add(CreateHealingItem("First Aid Kit", HealingType.FirstAidKit, ItemRarity.Uncommon, 75f, 6f, 0.75f, false));
        healingItems.Add(CreateHealingItem("Med Kit", HealingType.MedKit, ItemRarity.Rare, 100f, 8f, 1f, false));
        healingItems.Add(CreateHealingItem("Energy Drink", HealingType.EnergyDrink, ItemRarity.Common, 40f, 4f, 1f, true));
        healingItems.Add(CreateHealingItem("Painkiller", HealingType.Painkiller, ItemRarity.Uncommon, 60f, 6f, 1f, true));
        healingItems.Add(CreateHealingItem("Adrenaline Syringe", HealingType.AdrenalineSyringe, ItemRarity.Epic, 100f, 8f, 1f, true));
    }

    PUBGItem CreateHealingItem(string name, HealingType type, ItemRarity rarity, float healAmount, float useTime, float maxHealthPercent, bool isBoost)
    {
        PUBGItem healing = new PUBGItem();
        healing.itemName = name;
        healing.itemType = PUBGItemType.Healing;
        healing.healingType = type;
        healing.rarity = rarity;
        healing.healAmount = healAmount;
        healing.useTime = useTime;
        healing.maxHealthPercent = maxHealthPercent;
        healing.isBoostItem = isBoost;
        healing.isConsumable = true;
        healing.stackSize = isBoost ? 10 : 5;
        healing.weight = 0.5f;
        healing.description = $"Heals {healAmount} HP over {useTime}s";
        return healing;
    }

    void CreateDefaultArmorItems()
    {
        armorItems.Clear();

        // Helmets
        armorItems.Add(CreateArmorItem("Level 1 Helmet", 1, 0.3f, true, false, ItemRarity.Common));
        armorItems.Add(CreateArmorItem("Level 2 Helmet", 2, 0.4f, true, false, ItemRarity.Uncommon));
        armorItems.Add(CreateArmorItem("Level 3 Helmet", 3, 0.55f, true, false, ItemRarity.Rare));

        // Vests
        armorItems.Add(CreateArmorItem("Level 1 Vest", 1, 0.3f, false, true, ItemRarity.Common));
        armorItems.Add(CreateArmorItem("Level 2 Vest", 2, 0.4f, false, true, ItemRarity.Uncommon));
        armorItems.Add(CreateArmorItem("Level 3 Vest", 3, 0.55f, false, true, ItemRarity.Rare));
    }

    PUBGItem CreateArmorItem(string name, int level, float damageReduction, bool isHelmet, bool isVest, ItemRarity rarity)
    {
        PUBGItem armor = new PUBGItem();
        armor.itemName = name;
        armor.itemType = PUBGItemType.Armor;
        armor.armorLevel = level;
        armor.damageReduction = damageReduction;
        armor.isHelmet = isHelmet;
        armor.isVest = isVest;
        armor.rarity = rarity;
        armor.weight = 1f + level * 0.5f;
        armor.description = $"Level {level} {(isHelmet ? "Helmet" : "Vest")} - {damageReduction:P0} damage reduction";
        return armor;
    }

    void CreateDefaultBackpacks()
    {
        backpacks.Clear();

        backpacks.Add(CreateBackpack("Level 1 Backpack", 1, 170, ItemRarity.Common));
        backpacks.Add(CreateBackpack("Level 2 Backpack", 2, 270, ItemRarity.Uncommon));
        backpacks.Add(CreateBackpack("Level 3 Backpack", 3, 370, ItemRarity.Rare));
    }

    PUBGItem CreateBackpack(string name, int level, int capacityBonus, ItemRarity rarity)
    {
        PUBGItem backpack = new PUBGItem();
        backpack.itemName = name;
        backpack.itemType = PUBGItemType.Backpack;
        backpack.capacityBonus = capacityBonus;
        backpack.rarity = rarity;
        backpack.weight = 0f; // Backpacks don't add weight
        backpack.description = $"Level {level} Backpack - +{capacityBonus} capacity";
        return backpack;
    }

    void CreateDefaultAttachments()
    {
        attachments.Clear();

        // Scopes
        attachments.Add(CreateAttachment("Red Dot Sight", new string[] { "AR", "SMG" }, 0f, 0.1f, 0f, ItemRarity.Common));
        attachments.Add(CreateAttachment("2x Scope", new string[] { "AR", "DMR" }, 0f, 0.15f, 50f, ItemRarity.Uncommon));
        attachments.Add(CreateAttachment("4x Scope", new string[] { "AR", "DMR", "Sniper" }, 0f, 0.2f, 100f, ItemRarity.Rare));
        attachments.Add(CreateAttachment("8x Scope", new string[] { "DMR", "Sniper" }, 0f, 0.25f, 200f, ItemRarity.Epic));

        // Muzzle attachments
        attachments.Add(CreateAttachment("Compensator", new string[] { "AR", "SMG" }, 0.2f, 0.15f, 0f, ItemRarity.Uncommon));
        attachments.Add(CreateAttachment("Suppressor", new string[] { "AR", "SMG", "Sniper" }, 0.1f, 0.1f, 0f, ItemRarity.Rare));

        // Grips
        attachments.Add(CreateAttachment("Vertical Grip", new string[] { "AR", "SMG" }, 0.15f, 0.1f, 0f, ItemRarity.Common));
        attachments.Add(CreateAttachment("Angled Grip", new string[] { "AR" }, 0.1f, 0.15f, 0f, ItemRarity.Uncommon));
    }

    PUBGItem CreateAttachment(string name, string[] compatibleWeapons, float recoilReduction, float stabilityBonus, float rangeBonus, ItemRarity rarity)
    {
        PUBGItem attachment = new PUBGItem();
        attachment.itemName = name;
        attachment.itemType = PUBGItemType.Attachment;
        attachment.compatibleWeapons = compatibleWeapons;
        attachment.recoilReduction = recoilReduction;
        attachment.stabilityBonus = stabilityBonus;
        attachment.rangeBonus = rangeBonus;
        attachment.rarity = rarity;
        attachment.weight = 0.2f;
        attachment.description = $"Weapon attachment - {recoilReduction:P0} recoil reduction";
        return attachment;
    }

    void CreateDefaultThrowables()
    {
        throwables.Clear();

        throwables.Add(CreateThrowable("Frag Grenade", 5f, 100f, 30f, 4f, ItemRarity.Uncommon));
        throwables.Add(CreateThrowable("Smoke Grenade", 8f, 0f, 25f, 2.5f, ItemRarity.Common));
        throwables.Add(CreateThrowable("Stun Grenade", 6f, 50f, 28f, 2.5f, ItemRarity.Uncommon));
        throwables.Add(CreateThrowable("Molotov Cocktail", 4f, 80f, 20f, 3f, ItemRarity.Rare));
    }

    PUBGItem CreateThrowable(string name, float explosionRadius, float explosionDamage, float throwRange, float fuseTime, ItemRarity rarity)
    {
        PUBGItem throwable = new PUBGItem();
        throwable.itemName = name;
        throwable.itemType = PUBGItemType.Throwable;
        throwable.explosionRadius = explosionRadius;
        throwable.explosionDamage = explosionDamage;
        throwable.throwRange = throwRange;
        throwable.fuseTime = fuseTime;
        throwable.rarity = rarity;
        throwable.weight = 0.5f;
        throwable.stackSize = 5;
        throwable.isConsumable = true;
        throwable.description = $"Throwable - {explosionDamage} damage, {explosionRadius}m radius";
        return throwable;
    }

    public PUBGItem GetRandomItem(PUBGItemType itemType)
    {
        List<PUBGItem> itemList = GetItemList(itemType);
        if (itemList.Count == 0) return null;

        return itemList[Random.Range(0, itemList.Count)];
    }

    List<PUBGItem> GetItemList(PUBGItemType itemType)
    {
        switch (itemType)
        {
            case PUBGItemType.Weapon: return weapons;
            case PUBGItemType.Healing: return healingItems;
            case PUBGItemType.Armor: return armorItems;
            case PUBGItemType.Backpack: return backpacks;
            case PUBGItemType.Attachment: return attachments;
            case PUBGItemType.Throwable: return throwables;
            default: return new List<PUBGItem>();
        }
    }

    /// <summary>
    /// Get item by name from all categories
    /// </summary>
    public PUBGItem GetItemByName(string itemName)
    {
        // Search in all categories
        foreach (var item in weapons)
            if (item.itemName == itemName) return item;

        foreach (var item in healingItems)
            if (item.itemName == itemName) return item;

        foreach (var item in armorItems)
            if (item.itemName == itemName) return item;

        foreach (var item in backpacks)
            if (item.itemName == itemName) return item;

        foreach (var item in attachments)
            if (item.itemName == itemName) return item;

        foreach (var item in throwables)
            if (item.itemName == itemName) return item;

        return null;
    }

    /// <summary>
    /// Get best healing item for current health percentage
    /// </summary>
    public PUBGItem GetBestHealingItem(float healthPercent)
    {
        PUBGItem bestItem = null;
        float bestEfficiency = 0f;

        foreach (var item in healingItems)
        {
            // Calculate efficiency based on health needed and item properties
            float healthNeeded = 1f - healthPercent;
            float itemEfficiency = item.healAmount / item.useTime;

            // Prefer items that match health needs
            if (healthNeeded > 0.5f && item.healAmount >= 75f) // Severe injury - use medkit/first aid
            {
                if (itemEfficiency > bestEfficiency)
                {
                    bestEfficiency = itemEfficiency;
                    bestItem = item;
                }
            }
            else if (healthNeeded > 0.2f && item.healAmount >= 40f) // Moderate injury
            {
                if (itemEfficiency > bestEfficiency)
                {
                    bestEfficiency = itemEfficiency;
                    bestItem = item;
                }
            }
            else if (healthNeeded <= 0.2f && item.isBoostItem) // Minor injury - use boost items
            {
                if (itemEfficiency > bestEfficiency)
                {
                    bestEfficiency = itemEfficiency;
                    bestItem = item;
                }
            }
        }

        return bestItem;
    }

    /// <summary>
    /// Get total item count across all categories
    /// </summary>
    public int GetTotalItemCount()
    {
        return weapons.Count + healingItems.Count + armorItems.Count +
               backpacks.Count + attachments.Count + throwables.Count;
    }
}

/// <summary>
/// AI Loot Priority Configuration
/// </summary>
[System.Serializable]
public class LootPriorityConfig
{
    [Header("🎯 Priority Weights")]
    public float weaponPriority = 2.0f;
    public float healingPriority = 1.5f;
    public float armorPriority = 1.0f;
    public float backpackPriority = 0.8f;
    public float attachmentPriority = 0.7f;
    public float throwablePriority = 0.6f;

    [Header("🩺 Health-Based Priorities")]
    public AnimationCurve healingPriorityCurve = AnimationCurve.EaseInOut(0, 0, 1, 2);

    [Header("⚔️ Combat Priorities")]
    public float weaponUpgradePriority = 0.5f;
    public float ammoScarcityMultiplier = 1.5f;
}
