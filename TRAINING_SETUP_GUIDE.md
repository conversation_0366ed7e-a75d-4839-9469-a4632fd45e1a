# 🚀 SquadMate AI Training Setup Guide

## ✅ Fixed Compilation Issues

The following compilation errors have been resolved:

- ✅ Added missing `using System.Collections.Generic;` to `WeaponPickup.cs`
- ✅ Removed duplicate `AddHealing` and `RemoveHealing` methods from `InventorySystem.cs`
- ✅ Fixed `UnityEngine.UI` namespace issues in both UI scripts
- ✅ Created `SimpleLeaderboardUI.cs` as TextMeshPro-free alternative
- ✅ All scripts now compile successfully

## 🎯 Complete Training Setup

### 1. 📂 Files Created/Updated

#### Training Configuration

- `config/squadmate-ppo.yaml` - Updated PPO configuration for 5v5 arena combat
- `Assets/Scripts/Training/MLAgentsTrainingLauncher.cs` - Advanced training launcher with process management
- `Assets/Scripts/Training/SimpleTrainingLauncher.cs` - Simple training launcher (recommended)
- `Assets/Scripts/Training/TrainingSceneSetup.cs` - Automated scene setup
- `Assets/Scripts/Training/TrainingSetupValidator.cs` - Comprehensive setup validation

#### Performance Tracking

- `Assets/Scripts/UI/AgentStats.cs` - Individual agent performance tracking
- `Assets/Scripts/UI/LeaderboardUI.cs` - Real-time leaderboard display (requires TextMeshPro)
- `Assets/Scripts/UI/SimpleLeaderboardUI.cs` - Simple leaderboard (no TextMeshPro required)

### 2. 🏗️ Scene Setup Process

#### Option A: Automated Setup (Recommended)

1. Create new scene: `PUBGTrainingArena.unity`
2. Add `TrainingSceneSetup.cs` to an empty GameObject
3. Configure the setup parameters in inspector:
   - Set `arenaPrefab` to your TDM.fbx model
   - Configure spawn positions for Team A and Team B
   - Set zone positions (Alpha, Bravo, Charlie)
   - Assign weapon and healing prefabs
4. Right-click the component → "Setup Training Scene"

#### Option B: Manual Setup

1. **Arena**: Import TDM.fbx, set to static, add NavMeshSurface
2. **Spawns**: Create empty GameObjects for team spawn points
3. **Zones**: Create trigger colliders with ZoneMarker.cs components
4. **Loot**: Place weapon and medkit prefabs around the arena
5. **NavMesh**: Bake navigation mesh for agent movement

### 3. 🤖 Agent Configuration

#### SquadMate Agent Setup

```csharp
// Each agent needs these components:
- SquadMateAgent.cs (ML-Agents behavior)
- NavMeshAgent (movement)
- HealthSystem.cs (health management)
- InventorySystem.cs (weapon/item management)
- AgentStats.cs (performance tracking)
- DynamicRewardSystem.cs (advanced rewards)
```

#### Team Assignment

- Team A: Tag agents with "TeamA", assign blue team color
- Team B: Tag agents with "TeamB", assign red team color
- Set unique agent names for leaderboard tracking

### 4. 🎮 Game Manager Setup

Create GameManager GameObject with:

- `SquadManager.cs` - Team coordination and spawning
- `RoleAssigner.cs` - Assign roles (Assault, Support, Scout, Anchor)
- `DecisionTreeLoader.cs` - Load tactical behavior trees

### 5. 📊 UI Setup

#### Leaderboard Configuration

1. Create Canvas with LeaderboardUI.cs
2. Create row prefab with these child objects:
   - Rank (TextMeshPro)
   - Name (TextMeshPro)
   - Role (TextMeshPro)
   - Score (TextMeshPro)
   - KDR (TextMeshPro)
   - Kills (TextMeshPro)
   - Deaths (TextMeshPro)
   - Assists (TextMeshPro)
   - Revives (TextMeshPro)
   - Zones (TextMeshPro)
   - Accuracy (TextMeshPro)
   - DPM (TextMeshPro)
   - Status (TextMeshPro)

### 6. 🚀 Training Launch

#### One-Click Training (Recommended)

1. Add `MLAgentsTrainingLauncher.cs` to GameManager
2. Configure training parameters in inspector
3. Right-click component → "Start Training"
4. Monitor progress in Unity Console and TensorBoard

#### Manual Training Command

```bash
mlagents-learn config/squadmate-ppo.yaml --run-id=squadmate-arena-v1 --force
```

### 7. 📈 Training Configuration Details

#### PPO Hyperparameters (squadmate-ppo.yaml)

- **Batch Size**: 512 (good for complex multi-agent scenarios)
- **Buffer Size**: 20,480 (stores more experience for learning)
- **Learning Rate**: 3.0e-4 (stable learning rate)
- **Hidden Units**: 256 (increased for complex decision making)
- **Memory**: 64 sequence length, 512 memory size (for temporal reasoning)
- **Max Steps**: 5M (extended training for pro-level behavior)

#### Key Features

- ✅ Memory-enabled network for tactical sequences
- ✅ Normalized observations for stable training
- ✅ Optimized for 5v5 team combat scenarios
- ✅ TensorBoard integration for monitoring
- ✅ Automatic ONNX export for deployment

### 8. 🎯 Performance Monitoring

#### Real-Time Metrics

- **Combat**: Kills, Deaths, Assists, KDR, Accuracy
- **Support**: Revives, Medkits used, Damage dealt/taken
- **Tactical**: Zones captured/held, Time alive, Combat time
- **Team**: Win rate, Coordination score, Role effectiveness

#### Export Options

- CSV export for detailed analysis
- TensorBoard logs for training curves
- ONNX models for deployment

### 9. 🔧 Troubleshooting

#### Common Issues

1. **"No agents found"**: Ensure SquadMateAgent behavior name matches config
2. **NavMesh errors**: Rebake NavMesh after scene changes
3. **Training not starting**: Check ML-Agents installation with `pip list`
4. **Performance issues**: Reduce number of agents or environment complexity

#### Performance Optimization

- Use GPU training: `--torch-device=cuda`
- Multiple environments: `--num-envs=4`
- Reduce visual quality during training
- Monitor memory usage with Task Manager

### 10. 🎖️ Expected Training Results

#### Training Phases

1. **Phase 1 (0-500k steps)**: Basic movement and shooting
2. **Phase 2 (500k-2M steps)**: Team coordination and role specialization
3. **Phase 3 (2M-5M steps)**: Advanced tactics and pro-level play

#### Success Metrics

- **Accuracy**: >60% for assault roles, >40% for support
- **KDR**: >1.0 for experienced agents
- **Team Coordination**: Synchronized pushes and revives
- **Zone Control**: Strategic capture and defense

## 🏁 Quick Start Checklist

- [ ] Fix compilation errors (completed ✅)
- [ ] Create training scene with TrainingSceneSetup.cs
- [ ] Configure SquadMate agents with all required components
- [ ] Set up GameManager with team coordination scripts
- [ ] Create leaderboard UI for performance monitoring
- [ ] Configure PPO training parameters
- [ ] Launch training with MLAgentsTrainingLauncher.cs
- [ ] Monitor progress with TensorBoard and leaderboard
- [ ] Export ONNX model after training completion

**Ready to train pro-level SquadMate AI! 🎮🤖**
