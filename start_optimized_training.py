#!/usr/bin/env python3
"""
🚀 Optimized SquadMate AI Training Launcher
Comprehensive training script with automatic fixes and optimizations
"""

import os
import sys
import subprocess
import time
import json
from pathlib import Path

def print_header():
    print("=" * 60)
    print("🚀 SquadMate AI - Optimized Training Launcher")
    print("🎯 PUBG-Style Tactical AI Training")
    print("=" * 60)

def check_requirements():
    """Check if all requirements are met"""
    print("\n🔍 Checking requirements...")
    
    issues = []
    
    # Check Python version
    if sys.version_info < (3, 7):
        issues.append("❌ Python 3.7+ required")
    else:
        print("✅ Python version OK")
    
    # Check ML-Agents installation
    try:
        import mlagents
        print("✅ ML-Agents installed")
    except ImportError:
        issues.append("❌ ML-Agents not installed")
    
    # Check Unity project files
    required_files = [
        "Assets/Scripts/Agents/SquadMateAgent.cs",
        "Assets/Scripts/Environment/PUBGItemSystem.cs",
        "config/squadmate_unity6_config.yaml"
    ]
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            issues.append(f"❌ Missing: {file_path}")
    
    return issues

def install_mlagents():
    """Install ML-Agents if not present"""
    print("\n📦 Installing ML-Agents...")
    
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", 
            "mlagents==1.0.0", 
            "torch==2.0.1",
            "tensorboard"
        ], check=True)
        print("✅ ML-Agents installed successfully!")
        return True
    except subprocess.CalledProcessError:
        print("❌ Failed to install ML-Agents")
        return False

def setup_directories():
    """Create necessary directories"""
    print("\n📁 Setting up directories...")
    
    directories = ["results", "models", "checkpoints", "logs"]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✅ {directory}/")

def optimize_config():
    """Optimize training configuration"""
    print("\n⚙️ Optimizing training configuration...")
    
    config_path = "config/squadmate_unity6_config.yaml"
    
    if not os.path.exists(config_path):
        print(f"❌ Config file not found: {config_path}")
        return False
    
    # Read and validate config
    with open(config_path, 'r') as f:
        content = f.read()
    
    # Check for key optimizations
    optimizations = [
        "batch_size: 4096",
        "buffer_size: 40960",
        "time_scale: 20",
        "use_burst_compilation: true"
    ]
    
    for opt in optimizations:
        if opt in content:
            print(f"✅ {opt}")
        else:
            print(f"⚠️ Missing optimization: {opt}")
    
    return True

def check_unity_build():
    """Check if Unity build is available"""
    print("\n🎮 Checking Unity build...")
    
    # Look for Unity executable
    unity_paths = [
        "Build/SquadMate.exe",
        "Build/SquadMate.app",
        "Build/SquadMate.x86_64",
        "Build/SquadMate"
    ]
    
    for path in unity_paths:
        if os.path.exists(path):
            print(f"✅ Unity build found: {path}")
            return path
    
    print("⚠️ No Unity build found - will use Unity Editor")
    return None

def start_training():
    """Start the training process"""
    print("\n🚀 Starting SquadMate AI training...")
    
    # Training command
    cmd = [
        "mlagents-learn",
        "config/squadmate_unity6_config.yaml",
        "--run-id=squadmate_optimized_training",
        "--force",
        "--no-graphics"
    ]
    
    # Add Unity build if available
    unity_build = check_unity_build()
    if unity_build:
        cmd.extend(["--env", unity_build])
    
    print(f"📝 Training command: {' '.join(cmd)}")
    print("\n🎯 Training started! Monitor progress with:")
    print("   tensorboard --logdir results")
    print("\n⏱️ Expected training time: 2-6 hours for good results")
    print("🔄 Training will save checkpoints every 20,000 steps")
    
    try:
        # Start training
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True)
        
        # Monitor output
        for line in process.stdout:
            print(line.strip())
            
            # Check for success indicators
            if "Step:" in line and "Reward:" in line:
                print("✅ Training progressing normally")
            elif "ERROR" in line or "Exception" in line:
                print(f"❌ Training error: {line.strip()}")
        
        return_code = process.wait()
        
        if return_code == 0:
            print("\n🎉 Training completed successfully!")
        else:
            print(f"\n❌ Training failed with code: {return_code}")
            
        return return_code == 0
        
    except KeyboardInterrupt:
        print("\n⏹️ Training interrupted by user")
        process.terminate()
        return False
    except Exception as e:
        print(f"\n❌ Training error: {e}")
        return False

def generate_report():
    """Generate training report"""
    print("\n📊 Generating training report...")
    
    report = {
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        "config": "squadmate_unity6_config.yaml",
        "status": "completed",
        "notes": "Optimized PUBG-style training with comprehensive fixes"
    }
    
    # Save report
    with open("training_report.json", "w") as f:
        json.dump(report, f, indent=2)
    
    print("✅ Training report saved to training_report.json")

def main():
    """Main training launcher"""
    print_header()
    
    # Check requirements
    issues = check_requirements()
    
    if issues:
        print("\n❌ Issues found:")
        for issue in issues:
            print(f"   {issue}")
        
        # Try to fix ML-Agents issue
        if any("ML-Agents" in issue for issue in issues):
            if input("\n🔧 Install ML-Agents? (y/n): ").lower() == 'y':
                if not install_mlagents():
                    print("❌ Failed to install ML-Agents. Please install manually.")
                    return
        else:
            print("\n🔧 Please fix the issues above before training.")
            return
    
    # Setup and optimize
    setup_directories()
    optimize_config()
    
    # Confirm training start
    print("\n🎯 Ready to start SquadMate AI training!")
    print("📋 Training Features:")
    print("   ✅ PUBG-style combat behaviors")
    print("   ✅ Advanced loot prioritization")
    print("   ✅ Tactical healing and cover usage")
    print("   ✅ Team coordination and reviving")
    print("   ✅ Unity 6 optimizations")
    
    if input("\n🚀 Start training? (y/n): ").lower() != 'y':
        print("Training cancelled.")
        return
    
    # Start training
    success = start_training()
    
    if success:
        generate_report()
        print("\n🎉 SquadMate AI training completed successfully!")
        print("📁 Check the 'models' folder for trained .onnx files")
        print("📊 View training graphs: tensorboard --logdir results")
    else:
        print("\n❌ Training failed. Check logs for details.")

if __name__ == "__main__":
    main()
