using UnityEngine;
using System.IO;

/// <summary>
/// Simple training launcher that provides training commands and validation
/// Use this if the advanced MLAgentsTrainingLauncher has issues
/// </summary>
public class SimpleTrainingLauncher : MonoBehaviour
{
    [Header("🚀 Training Configuration")]
    public string configFileName = "squadmate-ppo.yaml";
    public string runId = "squadmate-arena-v1";
    public bool resumeTraining = false;

    [Header("📁 Paths")]
    public string configPath = "config";

    [TextArea(5, 10)]
    public string trainingCommand = "";

    void Start()
    {
        GenerateTrainingCommand();
        ValidateSetup();
    }

    [ContextMenu("Generate Training Command")]
    public void GenerateTrainingCommand()
    {
        string command = "mlagents-learn";
        
        // Config file
        command += $" {configPath}/{configFileName}";
        
        // Run ID
        command += $" --run-id={runId}";
        
        // Resume or force
        if (resumeTraining)
        {
            command += " --resume";
        }
        else
        {
            command += " --force";
        }

        trainingCommand = command;
        
        Debug.Log($"🚀 Training Command Generated:\n{trainingCommand}");
        Debug.Log("📝 Copy this command and run it in your terminal/command prompt");
    }

    [ContextMenu("Validate Setup")]
    public void ValidateSetup()
    {
        Debug.Log("🔍 Validating training setup...");
        
        bool isValid = true;

        // Check config file
        string fullConfigPath = Path.Combine(Application.dataPath, "..", configPath, configFileName);
        if (File.Exists(fullConfigPath))
        {
            Debug.Log($"✅ Config file found: {fullConfigPath}");
        }
        else
        {
            Debug.LogError($"❌ Config file not found: {fullConfigPath}");
            isValid = false;
        }

        // Check for SquadMateAgent in scene
        SquadMateAgent[] agents = FindObjectsOfType<SquadMateAgent>();
        if (agents.Length > 0)
        {
            Debug.Log($"✅ Found {agents.Length} SquadMateAgent(s) in scene");
        }
        else
        {
            Debug.LogWarning("⚠️ No SquadMateAgent found in scene. Add agents before training.");
        }

        // Check for AgentStats components
        AgentStats[] stats = FindObjectsOfType<AgentStats>();
        if (stats.Length > 0)
        {
            Debug.Log($"✅ Found {stats.Length} AgentStats component(s) for performance tracking");
        }
        else
        {
            Debug.LogWarning("⚠️ No AgentStats found. Add AgentStats components to track performance.");
        }

        // Check for leaderboard UI
        SimpleLeaderboardUI simpleUI = FindObjectOfType<SimpleLeaderboardUI>();
        LeaderboardUI advancedUI = FindObjectOfType<LeaderboardUI>();
        
        if (simpleUI != null || advancedUI != null)
        {
            Debug.Log("✅ Leaderboard UI found for performance monitoring");
        }
        else
        {
            Debug.LogWarning("⚠️ No leaderboard UI found. Add SimpleLeaderboardUI or LeaderboardUI for monitoring.");
        }

        if (isValid)
        {
            Debug.Log("🎯 Setup validation passed! Ready for training.");
        }
        else
        {
            Debug.LogError("❌ Setup validation failed. Fix the issues above before training.");
        }
    }

    [ContextMenu("Copy Training Command")]
    public void CopyTrainingCommand()
    {
        if (string.IsNullOrEmpty(trainingCommand))
        {
            GenerateTrainingCommand();
        }

        GUIUtility.systemCopyBuffer = trainingCommand;
        Debug.Log("📋 Training command copied to clipboard!");
    }

    [ContextMenu("Show Training Instructions")]
    public void ShowTrainingInstructions()
    {
        string instructions = @"
🚀 TRAINING INSTRUCTIONS:

1. Open Terminal/Command Prompt
2. Navigate to your project directory: cd ""C:\Squadmate""
3. Run the training command (see trainingCommand field above)
4. In Unity, click Play to start the training scene
5. Monitor progress in the terminal and Unity console

📊 MONITORING:
- Watch the leaderboard UI for real-time agent performance
- Check terminal for training progress and rewards
- Training will run for several hours (5M steps)

🛑 TO STOP TRAINING:
- Press Ctrl+C in the terminal
- Stop Play mode in Unity

📦 AFTER TRAINING:
- ONNX model will be exported to results folder
- Use the model for deployment in your game

🔧 TROUBLESHOOTING:
- If ML-Agents not found: pip install mlagents
- If config errors: check squadmate-ppo.yaml syntax
- If no agents: ensure SquadMateAgent behavior name matches config
";

        Debug.Log(instructions);
    }

    void OnValidate()
    {
        // Auto-generate command when values change in inspector
        if (Application.isPlaying)
        {
            GenerateTrainingCommand();
        }
    }

    #region Public Utility Methods
    public void SetRunId(string newRunId)
    {
        runId = newRunId;
        GenerateTrainingCommand();
    }

    public void ToggleResumeTraining()
    {
        resumeTraining = !resumeTraining;
        GenerateTrainingCommand();
    }

    public string GetTrainingCommand()
    {
        return trainingCommand;
    }
    #endregion
}
