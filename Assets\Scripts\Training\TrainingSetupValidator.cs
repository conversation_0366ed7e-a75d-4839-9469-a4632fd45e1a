using UnityEngine;
using UnityEngine.AI;
using System.IO;
using System.Linq;

/// <summary>
/// Comprehensive validation script for SquadMate AI training setup
/// Checks all components, files, and configurations needed for training
/// </summary>
public class TrainingSetupValidator : MonoBehaviour
{
    [Header("📋 Validation Results")]
    [TextArea(10, 15)]
    public string validationReport = "Click 'Validate Complete Setup' to run validation...";

    [Header("🔧 Auto-Fix Options")]
    public bool autoCreateMissingComponents = true;
    public bool autoAssignReferences = true;

    [ContextMenu("Validate Complete Setup")]
    public void ValidateCompleteSetup()
    {
        validationReport = "🔍 Running comprehensive training setup validation...\n\n";
        
        bool allValid = true;
        
        allValid &= ValidateConfigFiles();
        allValid &= ValidateSceneSetup();
        allValid &= ValidateAgentComponents();
        allValid &= ValidateUIComponents();
        allValid &= ValidateNavMesh();
        allValid &= ValidateTrainingComponents();
        
        if (allValid)
        {
            validationReport += "\n🎉 ALL VALIDATIONS PASSED! Ready for training! 🚀\n";
            validationReport += "\nNext steps:\n";
            validationReport += "1. Use SimpleTrainingLauncher to get training command\n";
            validationReport += "2. Run the command in terminal\n";
            validationReport += "3. Click Play in Unity to start training\n";
        }
        else
        {
            validationReport += "\n❌ Some validations failed. Fix the issues above before training.\n";
        }
        
        Debug.Log(validationReport);
    }

    bool ValidateConfigFiles()
    {
        validationReport += "📁 VALIDATING CONFIG FILES:\n";
        bool valid = true;

        // Check PPO config
        string configPath = Path.Combine(Application.dataPath, "..", "config", "squadmate-ppo.yaml");
        if (File.Exists(configPath))
        {
            validationReport += "✅ squadmate-ppo.yaml found\n";
        }
        else
        {
            validationReport += "❌ squadmate-ppo.yaml missing\n";
            valid = false;
        }

        return valid;
    }

    bool ValidateSceneSetup()
    {
        validationReport += "\n🏟️ VALIDATING SCENE SETUP:\n";
        bool valid = true;

        // Check for arena/environment
        GameObject[] staticObjects = FindObjectsOfType<GameObject>().Where(go => go.isStatic).ToArray();
        if (staticObjects.Length > 0)
        {
            validationReport += $"✅ Found {staticObjects.Length} static objects (arena geometry)\n";
        }
        else
        {
            validationReport += "⚠️ No static objects found - add arena geometry\n";
        }

        // Check for spawn points
        GameObject[] teamASpawns = GameObject.FindGameObjectsWithTag("TeamASpawn");
        GameObject[] teamBSpawns = GameObject.FindGameObjectsWithTag("TeamBSpawn");
        
        if (teamASpawns.Length > 0 && teamBSpawns.Length > 0)
        {
            validationReport += $"✅ Team spawns found (A: {teamASpawns.Length}, B: {teamBSpawns.Length})\n";
        }
        else
        {
            validationReport += "⚠️ Team spawn points missing - create spawn GameObjects with tags\n";
        }

        // Check for zones
        ZoneMarker[] zones = FindObjectsOfType<ZoneMarker>();
        if (zones.Length > 0)
        {
            validationReport += $"✅ Found {zones.Length} capture zones\n";
        }
        else
        {
            validationReport += "⚠️ No capture zones found - add ZoneMarker components\n";
        }

        return valid;
    }

    bool ValidateAgentComponents()
    {
        validationReport += "\n🤖 VALIDATING AGENT COMPONENTS:\n";
        bool valid = true;

        SquadMateAgent[] agents = FindObjectsOfType<SquadMateAgent>();
        if (agents.Length == 0)
        {
            validationReport += "❌ No SquadMateAgent found in scene\n";
            return false;
        }

        validationReport += $"✅ Found {agents.Length} SquadMateAgent(s)\n";

        foreach (var agent in agents)
        {
            string agentName = agent.gameObject.name;
            
            // Check required components
            NavMeshAgent navAgent = agent.GetComponent<NavMeshAgent>();
            HealthSystem health = agent.GetComponent<HealthSystem>();
            InventorySystem inventory = agent.GetComponent<InventorySystem>();
            AgentStats stats = agent.GetComponent<AgentStats>();

            if (navAgent == null)
            {
                validationReport += $"❌ {agentName}: Missing NavMeshAgent\n";
                if (autoCreateMissingComponents)
                {
                    agent.gameObject.AddComponent<NavMeshAgent>();
                    validationReport += $"🔧 {agentName}: Added NavMeshAgent\n";
                }
                else
                {
                    valid = false;
                }
            }

            if (health == null)
            {
                validationReport += $"⚠️ {agentName}: Missing HealthSystem\n";
                if (autoCreateMissingComponents)
                {
                    agent.gameObject.AddComponent<HealthSystem>();
                    validationReport += $"🔧 {agentName}: Added HealthSystem\n";
                }
            }

            if (inventory == null)
            {
                validationReport += $"⚠️ {agentName}: Missing InventorySystem\n";
                if (autoCreateMissingComponents)
                {
                    agent.gameObject.AddComponent<InventorySystem>();
                    validationReport += $"🔧 {agentName}: Added InventorySystem\n";
                }
            }

            if (stats == null)
            {
                validationReport += $"⚠️ {agentName}: Missing AgentStats\n";
                if (autoCreateMissingComponents)
                {
                    agent.gameObject.AddComponent<AgentStats>();
                    validationReport += $"🔧 {agentName}: Added AgentStats\n";
                }
            }
        }

        return valid;
    }

    bool ValidateUIComponents()
    {
        validationReport += "\n📊 VALIDATING UI COMPONENTS:\n";
        bool valid = true;

        SimpleLeaderboardUI simpleUI = FindObjectOfType<SimpleLeaderboardUI>();
        LeaderboardUI advancedUI = FindObjectOfType<LeaderboardUI>();

        if (simpleUI != null)
        {
            validationReport += "✅ SimpleLeaderboardUI found\n";
        }
        else if (advancedUI != null)
        {
            validationReport += "✅ LeaderboardUI found\n";
        }
        else
        {
            validationReport += "⚠️ No leaderboard UI found - add for performance monitoring\n";
        }

        return valid;
    }

    bool ValidateNavMesh()
    {
        validationReport += "\n🗺️ VALIDATING NAVMESH:\n";
        bool valid = true;

        NavMeshSurface navSurface = FindObjectOfType<NavMeshSurface>();
        if (navSurface != null)
        {
            validationReport += "✅ NavMeshSurface found\n";
        }
        else
        {
            validationReport += "⚠️ NavMeshSurface not found - add for agent navigation\n";
        }

        // Check if NavMesh data exists
        NavMeshTriangulation navMeshData = NavMesh.CalculateTriangulation();
        if (navMeshData.vertices.Length > 0)
        {
            validationReport += $"✅ NavMesh baked ({navMeshData.vertices.Length} vertices)\n";
        }
        else
        {
            validationReport += "❌ NavMesh not baked - bake NavMesh for agent movement\n";
            valid = false;
        }

        return valid;
    }

    bool ValidateTrainingComponents()
    {
        validationReport += "\n🚀 VALIDATING TRAINING COMPONENTS:\n";
        bool valid = true;

        // Check for training launchers
        SimpleTrainingLauncher simpleLauncher = FindObjectOfType<SimpleTrainingLauncher>();
        MLAgentsTrainingLauncher advancedLauncher = FindObjectOfType<MLAgentsTrainingLauncher>();

        if (simpleLauncher != null || advancedLauncher != null)
        {
            validationReport += "✅ Training launcher found\n";
        }
        else
        {
            validationReport += "⚠️ No training launcher found - add for easy training management\n";
        }

        // Check for game manager
        SquadManager squadManager = FindObjectOfType<SquadManager>();
        if (squadManager != null)
        {
            validationReport += "✅ SquadManager found\n";
        }
        else
        {
            validationReport += "⚠️ SquadManager not found - add for team coordination\n";
        }

        return valid;
    }

    [ContextMenu("Auto-Fix Common Issues")]
    public void AutoFixCommonIssues()
    {
        Debug.Log("🔧 Auto-fixing common setup issues...");
        
        // Add missing components to agents
        SquadMateAgent[] agents = FindObjectsOfType<SquadMateAgent>();
        foreach (var agent in agents)
        {
            if (agent.GetComponent<NavMeshAgent>() == null)
                agent.gameObject.AddComponent<NavMeshAgent>();
            
            if (agent.GetComponent<HealthSystem>() == null)
                agent.gameObject.AddComponent<HealthSystem>();
            
            if (agent.GetComponent<InventorySystem>() == null)
                agent.gameObject.AddComponent<InventorySystem>();
            
            if (agent.GetComponent<AgentStats>() == null)
                agent.gameObject.AddComponent<AgentStats>();
        }

        // Add NavMeshSurface if missing
        if (FindObjectOfType<NavMeshSurface>() == null)
        {
            GameObject navMeshObject = new GameObject("NavMeshSurface");
            navMeshObject.AddComponent<NavMeshSurface>();
        }

        Debug.Log("✅ Auto-fix completed! Run validation again to check results.");
    }
}
