using UnityEngine;
using System.Collections.Generic;
using System.Collections;

/// <summary>
/// Capture Zone for Team Deathmatch
/// Provides tactical objectives and rewards for zone control
/// </summary>
public class CaptureZone : MonoBehaviour
{
    [Header("🎯 Zone Configuration")]
    public string zoneName = "Capture Zone";
    public float captureRadius = 5f;
    public float captureTime = 10f;
    public float controlPointValue = 1f;
    
    [Header("🎨 Visual Settings")]
    public Color neutralColor = Color.white;
    public Color teamAColor = Color.blue;
    public Color teamBColor = Color.red;
    public Color contestedColor = Color.yellow;
    
    [Header("🔊 Audio Settings")]
    public AudioClip captureStartSound;
    public AudioClip captureCompleteSound;
    public AudioClip contestedSound;
    
    [Header("📊 Zone Status")]
    public ZoneState currentState = ZoneState.Neutral;
    public TeamID controllingTeam = TeamID.TeamA;
    public float captureProgress = 0f;
    public int teamAAgentsInZone = 0;
    public int teamBAgentsInZone = 0;
    
    // Components
    private Renderer zoneRenderer;
    private AudioSource audioSource;
    private SphereCollider zoneCollider;
    private ParticleSystem captureEffect;
    
    // Zone tracking
    private List<VictorAgent> agentsInZone = new List<VictorAgent>();
    private float lastUpdateTime;
    private bool isBeingCaptured = false;
    
    // Events
    public System.Action<CaptureZone, TeamID> OnZoneCaptured;
    public System.Action<CaptureZone> OnZoneContested;
    public System.Action<CaptureZone> OnZoneNeutralized;
    
    public enum ZoneState
    {
        Neutral,
        BeingCaptured,
        Controlled,
        Contested
    }
    
    void Awake()
    {
        SetupZoneComponents();
    }
    
    void SetupZoneComponents()
    {
        // Create zone collider
        zoneCollider = GetComponent<SphereCollider>();
        if (zoneCollider == null)
        {
            zoneCollider = gameObject.AddComponent<SphereCollider>();
        }
        zoneCollider.isTrigger = true;
        zoneCollider.radius = captureRadius;
        
        // Create visual representation
        if (GetComponent<Renderer>() == null)
        {
            GameObject visual = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
            visual.transform.SetParent(transform);
            visual.transform.localPosition = Vector3.zero;
            visual.transform.localScale = new Vector3(captureRadius * 2, 0.1f, captureRadius * 2);
            
            // Remove the collider from visual (we use our own)
            DestroyImmediate(visual.GetComponent<Collider>());
            
            zoneRenderer = visual.GetComponent<Renderer>();
        }
        else
        {
            zoneRenderer = GetComponent<Renderer>();
        }
        
        // Setup audio
        audioSource = GetComponent<AudioSource>();
        if (audioSource == null)
        {
            audioSource = gameObject.AddComponent<AudioSource>();
        }
        audioSource.playOnAwake = false;
        audioSource.spatialBlend = 1f; // 3D sound
        
        // Create particle effect
        CreateCaptureEffect();
        
        // Initialize visual state
        UpdateVisuals();
    }
    
    void CreateCaptureEffect()
    {
        GameObject effectObj = new GameObject("CaptureEffect");
        effectObj.transform.SetParent(transform);
        effectObj.transform.localPosition = Vector3.up * 2f;
        
        captureEffect = effectObj.AddComponent<ParticleSystem>();
        var main = captureEffect.main;
        main.startLifetime = 2f;
        main.startSpeed = 5f;
        main.maxParticles = 50;
        main.startColor = neutralColor;
        
        var emission = captureEffect.emission;
        emission.rateOverTime = 10f;
        
        var shape = captureEffect.shape;
        shape.shapeType = ParticleSystemShapeType.Circle;
        shape.radius = captureRadius;
        
        captureEffect.Stop();
    }
    
    public void Initialize(string name, float radius, float time)
    {
        zoneName = name;
        captureRadius = radius;
        captureTime = time;
        
        if (zoneCollider != null)
        {
            zoneCollider.radius = captureRadius;
        }
        
        gameObject.name = $"CaptureZone_{name}";
    }
    
    void OnTriggerEnter(Collider other)
    {
        VictorAgent agent = other.GetComponent<VictorAgent>();
        if (agent != null && !agentsInZone.Contains(agent))
        {
            agentsInZone.Add(agent);
            UpdateZoneCount();
            
            // Small reward for entering zone
            agent.AddReward(0.05f);
            
            Debug.Log($"🎯 {agent.name} entered {zoneName}");
        }
    }
    
    void OnTriggerExit(Collider other)
    {
        VictorAgent agent = other.GetComponent<VictorAgent>();
        if (agent != null && agentsInZone.Contains(agent))
        {
            agentsInZone.Remove(agent);
            UpdateZoneCount();
            
            Debug.Log($"🚪 {agent.name} left {zoneName}");
        }
    }
    
    void UpdateZoneCount()
    {
        teamAAgentsInZone = 0;
        teamBAgentsInZone = 0;
        
        // Clean up null references
        agentsInZone.RemoveAll(agent => agent == null);
        
        // Count agents by team
        foreach (VictorAgent agent in agentsInZone)
        {
            if (agent.currentHealth > 0) // Only count living agents
            {
                if (agent.teamID == TeamID.TeamA)
                {
                    teamAAgentsInZone++;
                }
                else
                {
                    teamBAgentsInZone++;
                }
            }
        }
    }
    
    public void UpdateZone()
    {
        UpdateZoneCount();
        
        float deltaTime = Time.time - lastUpdateTime;
        lastUpdateTime = Time.time;
        
        // Determine zone state
        ZoneState previousState = currentState;
        DetermineZoneState();
        
        // Update capture progress
        UpdateCaptureProgress(deltaTime);
        
        // Handle state changes
        if (previousState != currentState)
        {
            OnStateChanged(previousState, currentState);
        }
        
        // Update visuals
        UpdateVisuals();
        
        // Reward agents in zone
        RewardAgentsInZone();
    }
    
    void DetermineZoneState()
    {
        if (teamAAgentsInZone > 0 && teamBAgentsInZone > 0)
        {
            currentState = ZoneState.Contested;
        }
        else if (teamAAgentsInZone > 0)
        {
            if (controllingTeam == TeamID.TeamA && captureProgress >= 1f)
            {
                currentState = ZoneState.Controlled;
            }
            else
            {
                currentState = ZoneState.BeingCaptured;
                controllingTeam = TeamID.TeamA;
            }
        }
        else if (teamBAgentsInZone > 0)
        {
            if (controllingTeam == TeamID.TeamB && captureProgress >= 1f)
            {
                currentState = ZoneState.Controlled;
            }
            else
            {
                currentState = ZoneState.BeingCaptured;
                controllingTeam = TeamID.TeamB;
            }
        }
        else
        {
            currentState = ZoneState.Neutral;
        }
    }
    
    void UpdateCaptureProgress(float deltaTime)
    {
        switch (currentState)
        {
            case ZoneState.BeingCaptured:
                captureProgress += deltaTime / captureTime;
                captureProgress = Mathf.Clamp01(captureProgress);
                
                if (captureProgress >= 1f)
                {
                    OnZoneCaptured?.Invoke(this, controllingTeam);
                    PlaySound(captureCompleteSound);
                }
                break;
                
            case ZoneState.Contested:
                // Slowly decay progress when contested
                captureProgress -= deltaTime / (captureTime * 2f);
                captureProgress = Mathf.Clamp01(captureProgress);
                break;
                
            case ZoneState.Neutral:
                // Reset progress when neutral
                captureProgress = 0f;
                break;
        }
    }
    
    void OnStateChanged(ZoneState from, ZoneState to)
    {
        Debug.Log($"🎯 {zoneName}: {from} → {to}");
        
        switch (to)
        {
            case ZoneState.BeingCaptured:
                if (!isBeingCaptured)
                {
                    isBeingCaptured = true;
                    PlaySound(captureStartSound);
                    captureEffect.Play();
                }
                break;
                
            case ZoneState.Contested:
                OnZoneContested?.Invoke(this);
                PlaySound(contestedSound);
                break;
                
            case ZoneState.Controlled:
                isBeingCaptured = false;
                captureEffect.Stop();
                break;
                
            case ZoneState.Neutral:
                isBeingCaptured = false;
                captureEffect.Stop();
                OnZoneNeutralized?.Invoke(this);
                break;
        }
    }
    
    void UpdateVisuals()
    {
        if (zoneRenderer == null) return;
        
        Color targetColor = neutralColor;
        
        switch (currentState)
        {
            case ZoneState.Neutral:
                targetColor = neutralColor;
                break;
            case ZoneState.BeingCaptured:
            case ZoneState.Controlled:
                targetColor = (controllingTeam == TeamID.TeamA) ? teamAColor : teamBColor;
                break;
            case ZoneState.Contested:
                targetColor = contestedColor;
                break;
        }
        
        // Lerp color based on capture progress
        if (currentState == ZoneState.BeingCaptured)
        {
            targetColor = Color.Lerp(neutralColor, targetColor, captureProgress);
        }
        
        zoneRenderer.material.color = targetColor;
        
        // Update particle effect color
        if (captureEffect != null)
        {
            var main = captureEffect.main;
            main.startColor = targetColor;
        }
    }
    
    void RewardAgentsInZone()
    {
        foreach (VictorAgent agent in agentsInZone)
        {
            if (agent != null && agent.currentHealth > 0)
            {
                // Reward for zone presence
                agent.AddReward(0.01f);
                
                // Extra reward for controlling team
                if (currentState == ZoneState.Controlled && agent.teamID == controllingTeam)
                {
                    agent.AddReward(0.02f);
                }
                
                // Bonus for contested zones (encourages fighting)
                if (currentState == ZoneState.Contested)
                {
                    agent.AddReward(0.015f);
                }
            }
        }
    }
    
    void PlaySound(AudioClip clip)
    {
        if (audioSource != null && clip != null)
        {
            audioSource.PlayOneShot(clip);
        }
    }
    
    public bool IsControlledBy(TeamID team)
    {
        return currentState == ZoneState.Controlled && controllingTeam == team;
    }
    
    public float GetControlPercentage()
    {
        return captureProgress;
    }
    
    public int GetAgentCount(TeamID team)
    {
        return (team == TeamID.TeamA) ? teamAAgentsInZone : teamBAgentsInZone;
    }
    
    void OnDrawGizmosSelected()
    {
        // Draw capture radius
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(transform.position, captureRadius);
        
        // Draw capture progress
        if (Application.isPlaying && captureProgress > 0)
        {
            Gizmos.color = (controllingTeam == TeamID.TeamA) ? Color.blue : Color.red;
            Gizmos.DrawSphere(transform.position + Vector3.up * 3f, captureProgress);
        }
    }
}
