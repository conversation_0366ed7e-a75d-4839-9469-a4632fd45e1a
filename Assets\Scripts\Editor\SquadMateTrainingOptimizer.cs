using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// Comprehensive training optimizer for SquadMate AI
/// Fixes common issues and optimizes training performance
/// </summary>
public class SquadMateTrainingOptimizer : EditorWindow
{
    private Vector2 scrollPosition;
    private bool showAdvancedOptions = false;
    
    [MenuItem("SquadMate AI/🚀 Training Optimizer")]
    public static void ShowWindow()
    {
        GetWindow<SquadMateTrainingOptimizer>("SquadMate Training Optimizer");
    }

    void OnGUI()
    {
        scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
        
        GUILayout.Label("🚀 SquadMate AI Training Optimizer", EditorStyles.boldLabel);
        GUILayout.Space(10);

        // Quick Fix Section
        GUILayout.Label("⚡ Quick Fixes", EditorStyles.boldLabel);
        
        if (GUILayout.Button("🔧 Fix All Common Issues"))
        {
            FixAllCommonIssues();
        }
        
        if (GUILayout.Button("🧪 Validate PUBG Systems"))
        {
            ValidatePUBGSystems();
        }
        
        if (GUILayout.Button("📊 Optimize Training Performance"))
        {
            OptimizeTrainingPerformance();
        }

        GUILayout.Space(10);

        // Individual Fixes
        GUILayout.Label("🔨 Individual Fixes", EditorStyles.boldLabel);
        
        if (GUILayout.Button("Fix Missing Components"))
        {
            FixMissingComponents();
        }
        
        if (GUILayout.Button("Setup PUBG Item Database"))
        {
            SetupPUBGItemDatabase();
        }
        
        if (GUILayout.Button("Fix Agent Observations"))
        {
            FixAgentObservations();
        }
        
        if (GUILayout.Button("Optimize Reward System"))
        {
            OptimizeRewardSystem();
        }

        GUILayout.Space(10);

        // Advanced Options
        showAdvancedOptions = EditorGUILayout.Foldout(showAdvancedOptions, "🔬 Advanced Options");
        if (showAdvancedOptions)
        {
            if (GUILayout.Button("Reset All Training Data"))
            {
                if (EditorUtility.DisplayDialog("Reset Training Data", 
                    "This will delete all training checkpoints and results. Continue?", "Yes", "No"))
                {
                    ResetTrainingData();
                }
            }
            
            if (GUILayout.Button("Generate Training Report"))
            {
                GenerateTrainingReport();
            }
            
            if (GUILayout.Button("Export Agent Configuration"))
            {
                ExportAgentConfiguration();
            }
        }

        EditorGUILayout.EndScrollView();
    }

    void FixAllCommonIssues()
    {
        Debug.Log("🔧 Starting comprehensive fix...");
        
        FixMissingComponents();
        SetupPUBGItemDatabase();
        FixAgentObservations();
        OptimizeRewardSystem();
        ValidatePUBGSystems();
        
        Debug.Log("✅ All common issues fixed!");
        EditorUtility.DisplayDialog("Success", "All common issues have been fixed!", "OK");
    }

    void FixMissingComponents()
    {
        Debug.Log("🔧 Fixing missing components...");
        
        SquadMateAgent[] agents = FindObjectsOfType<SquadMateAgent>();
        
        foreach (SquadMateAgent agent in agents)
        {
            // Ensure all required components exist
            if (agent.GetComponent<PUBGInventory>() == null)
            {
                agent.gameObject.AddComponent<PUBGInventory>();
                Debug.Log($"✅ Added PUBGInventory to {agent.name}");
            }
            
            if (agent.GetComponent<PUBGArmorSystem>() == null)
            {
                agent.gameObject.AddComponent<PUBGArmorSystem>();
                Debug.Log($"✅ Added PUBGArmorSystem to {agent.name}");
            }
            
            if (agent.GetComponent<WeaponSystem>() == null)
            {
                agent.gameObject.AddComponent<WeaponSystem>();
                Debug.Log($"✅ Added WeaponSystem to {agent.name}");
            }
            
            if (agent.GetComponent<HealthSystem>() == null)
            {
                agent.gameObject.AddComponent<HealthSystem>();
                Debug.Log($"✅ Added HealthSystem to {agent.name}");
            }
            
            if (agent.GetComponent<DynamicRewardSystem>() == null)
            {
                agent.gameObject.AddComponent<DynamicRewardSystem>();
                Debug.Log($"✅ Added DynamicRewardSystem to {agent.name}");
            }

            // Ensure Rigidbody is properly configured
            Rigidbody rb = agent.GetComponent<Rigidbody>();
            if (rb == null)
            {
                rb = agent.gameObject.AddComponent<Rigidbody>();
            }
            rb.constraints = RigidbodyConstraints.FreezeRotationX | RigidbodyConstraints.FreezeRotationZ;
            rb.mass = 1f;
            rb.drag = 5f;
            rb.angularDrag = 5f;
        }
        
        Debug.Log("✅ Missing components fixed!");
    }

    void SetupPUBGItemDatabase()
    {
        Debug.Log("📦 Setting up PUBG Item Database...");
        
        // Find or create PUBGItemSystem
        PUBGItemSystem itemSystem = FindObjectOfType<PUBGItemSystem>();
        if (itemSystem == null)
        {
            GameObject itemSystemObj = new GameObject("PUBGItemSystem");
            itemSystem = itemSystemObj.AddComponent<PUBGItemSystem>();
            Debug.Log("✅ Created PUBGItemSystem");
        }
        
        // Create item database if missing
        if (itemSystem.itemDatabase == null)
        {
            itemSystem.itemDatabase = ScriptableObject.CreateInstance<PUBGItemDatabase>();
            itemSystem.itemDatabase.InitializeDefaultItems();
            
            // Save as asset
            string path = "Assets/Data/PUBGItemDatabase.asset";
            System.IO.Directory.CreateDirectory("Assets/Data");
            AssetDatabase.CreateAsset(itemSystem.itemDatabase, path);
            AssetDatabase.SaveAssets();
            
            Debug.Log($"✅ Created PUBGItemDatabase at {path}");
        }
        
        Debug.Log("✅ PUBG Item Database setup complete!");
    }

    void FixAgentObservations()
    {
        Debug.Log("👁️ Fixing agent observations...");
        
        SquadMateAgent[] agents = FindObjectsOfType<SquadMateAgent>();
        
        foreach (SquadMateAgent agent in agents)
        {
            // Ensure environment reference is set
            if (agent.environment == null)
            {
                agent.environment = FindObjectOfType<GameEnvironment>();
                if (agent.environment != null)
                {
                    Debug.Log($"✅ Set environment reference for {agent.name}");
                }
            }
            
            // Ensure item system reference is set
            if (agent.itemSystem == null)
            {
                agent.itemSystem = FindObjectOfType<PUBGItemSystem>();
                if (agent.itemSystem != null)
                {
                    Debug.Log($"✅ Set item system reference for {agent.name}");
                }
            }
        }
        
        Debug.Log("✅ Agent observations fixed!");
    }

    void OptimizeRewardSystem()
    {
        Debug.Log("🎯 Optimizing reward system...");
        
        DynamicRewardSystem[] rewardSystems = FindObjectsOfType<DynamicRewardSystem>();
        
        foreach (DynamicRewardSystem rewardSystem in rewardSystems)
        {
            // Optimize reward values for better learning
            rewardSystem.lootPickupReward = 0.3f;
            rewardSystem.weaponPickupReward = 0.5f;
            rewardSystem.healingReward = 0.4f;
            rewardSystem.combatReward = 0.2f;
            rewardSystem.survivalReward = 0.1f;
            
            Debug.Log($"✅ Optimized rewards for {rewardSystem.name}");
        }
        
        Debug.Log("✅ Reward system optimized!");
    }

    void ValidatePUBGSystems()
    {
        Debug.Log("🧪 Validating PUBG systems...");
        
        List<string> issues = new List<string>();
        
        // Check for SquadMateAgent
        SquadMateAgent agent = FindObjectOfType<SquadMateAgent>();
        if (agent == null)
        {
            issues.Add("❌ No SquadMateAgent found in scene");
        }
        else
        {
            Debug.Log("✅ SquadMateAgent found");
            
            // Check agent components
            if (agent.inventory == null) issues.Add("❌ Agent missing PUBGInventory");
            if (agent.armorSystem == null) issues.Add("❌ Agent missing PUBGArmorSystem");
            if (agent.itemSystem == null) issues.Add("❌ Agent missing PUBGItemSystem reference");
        }
        
        // Check for PUBGItemSystem
        PUBGItemSystem itemSystem = FindObjectOfType<PUBGItemSystem>();
        if (itemSystem == null)
        {
            issues.Add("❌ No PUBGItemSystem found in scene");
        }
        else if (itemSystem.itemDatabase == null)
        {
            issues.Add("❌ PUBGItemSystem missing item database");
        }
        else
        {
            Debug.Log("✅ PUBGItemSystem validated");
        }
        
        // Check for GameEnvironment
        GameEnvironment environment = FindObjectOfType<GameEnvironment>();
        if (environment == null)
        {
            issues.Add("❌ No GameEnvironment found in scene");
        }
        else
        {
            Debug.Log("✅ GameEnvironment found");
        }
        
        // Display results
        if (issues.Count == 0)
        {
            Debug.Log("✅ All PUBG systems validated successfully!");
            EditorUtility.DisplayDialog("Validation Success", "All PUBG systems are properly configured!", "OK");
        }
        else
        {
            string issueList = string.Join("\n", issues);
            Debug.LogWarning($"⚠️ Validation issues found:\n{issueList}");
            EditorUtility.DisplayDialog("Validation Issues", $"Issues found:\n{issueList}", "OK");
        }
    }

    void OptimizeTrainingPerformance()
    {
        Debug.Log("📊 Optimizing training performance...");
        
        // Set optimal quality settings for training
        QualitySettings.vSyncCount = 0;
        QualitySettings.shadows = ShadowQuality.Disable;
        QualitySettings.shadowResolution = ShadowResolution.Low;
        QualitySettings.antiAliasing = 0;
        
        // Optimize physics settings
        Physics.defaultSolverIterations = 4;
        Physics.defaultSolverVelocityIterations = 1;
        
        Debug.Log("✅ Training performance optimized!");
    }

    void ResetTrainingData()
    {
        Debug.Log("🗑️ Resetting training data...");
        
        // Delete training results
        if (System.IO.Directory.Exists("results"))
        {
            System.IO.Directory.Delete("results", true);
            Debug.Log("✅ Deleted results directory");
        }
        
        if (System.IO.Directory.Exists("models"))
        {
            System.IO.Directory.Delete("models", true);
            Debug.Log("✅ Deleted models directory");
        }
        
        Debug.Log("✅ Training data reset complete!");
    }

    void GenerateTrainingReport()
    {
        Debug.Log("📋 Generating training report...");
        
        string report = "# SquadMate AI Training Report\n\n";
        report += $"Generated: {System.DateTime.Now}\n\n";
        
        // System info
        report += "## System Configuration\n";
        report += $"- Unity Version: {Application.unityVersion}\n";
        report += $"- Platform: {Application.platform}\n";
        report += $"- ML-Agents: Installed\n\n";
        
        // Scene analysis
        SquadMateAgent[] agents = FindObjectsOfType<SquadMateAgent>();
        report += $"## Scene Analysis\n";
        report += $"- SquadMate Agents: {agents.Length}\n";
        report += $"- PUBG Item Systems: {FindObjectsOfType<PUBGItemSystem>().Length}\n";
        report += $"- Game Environments: {FindObjectsOfType<GameEnvironment>().Length}\n\n";
        
        // Save report
        string path = "SquadMate_Training_Report.md";
        System.IO.File.WriteAllText(path, report);
        
        Debug.Log($"✅ Training report saved to {path}");
        EditorUtility.DisplayDialog("Report Generated", $"Training report saved to {path}", "OK");
    }

    void ExportAgentConfiguration()
    {
        Debug.Log("📤 Exporting agent configuration...");
        
        SquadMateAgent agent = FindObjectOfType<SquadMateAgent>();
        if (agent == null)
        {
            EditorUtility.DisplayDialog("Error", "No SquadMateAgent found in scene!", "OK");
            return;
        }
        
        // Create configuration JSON
        var config = new
        {
            agentName = agent.name,
            moveSpeed = agent.moveSpeed,
            rotationSpeed = agent.rotationSpeed,
            maxHealth = agent.maxHealth,
            reviveRange = agent.reviveRange,
            followDistance = agent.followDistance,
            combatTimeout = agent.combatTimeout,
            hasInventory = agent.inventory != null,
            hasArmorSystem = agent.armorSystem != null,
            hasWeaponSystem = agent.GetComponent<WeaponSystem>() != null
        };
        
        string json = JsonUtility.ToJson(config, true);
        string path = "SquadMate_Agent_Config.json";
        System.IO.File.WriteAllText(path, json);
        
        Debug.Log($"✅ Agent configuration exported to {path}");
        EditorUtility.DisplayDialog("Export Complete", $"Agent configuration exported to {path}", "OK");
    }
}
