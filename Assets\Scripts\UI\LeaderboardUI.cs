using UnityEngine;
using UnityEngine.UI;
using System.Linq;
using System.Collections.Generic;

// Note: Using standard Unity UI Text components for compatibility

/// <summary>
/// Real-time leaderboard UI for tracking agent performance during training
/// </summary>
public class LeaderboardUI : MonoBehaviour
{
    [Header("🏆 UI References")]
    public GameObject rowPrefab;
    public Transform leaderboardContainer;
    public Text titleText;
    public Text roundInfoText;

    [Header("⚙️ Settings")]
    public float refreshRate = 2f;
    public int maxDisplayedAgents = 10;
    public bool sortByScore = true;
    public bool showTeamColors = true;

    [Header("🎨 Team Colors")]
    public Color teamAColor = Color.blue;
    public Color teamBColor = Color.red;
    public Color neutralColor = Color.white;

    private List<AgentStats> cachedAgents = new List<AgentStats>();
    private float lastUpdateTime = 0f;

    void Start()
    {
        if (titleText != null)
        {
            titleText.text = "🏆 SquadMate AI Leaderboard";
        }

        InvokeRepeating(nameof(UpdateLeaderboard), 1f, refreshRate);
    }

    void UpdateLeaderboard()
    {
        // Clear existing rows
        foreach (Transform child in leaderboardContainer)
        {
            if (child != leaderboardContainer)
            {
                Destroy(child.gameObject);
            }
        }

        // Find all agents with stats
        AgentStats[] allAgents = FindObjectsOfType<AgentStats>();

        if (allAgents.Length == 0)
        {
            CreateNoDataRow();
            return;
        }

        // Sort agents based on selected criteria
        var sortedAgents = SortAgents(allAgents);

        // Update round info
        UpdateRoundInfo(allAgents);

        // Create rows for top agents
        int displayCount = Mathf.Min(maxDisplayedAgents, sortedAgents.Length);
        for (int i = 0; i < displayCount; i++)
        {
            CreateAgentRow(sortedAgents[i], i + 1);
        }

        lastUpdateTime = Time.time;
    }

    AgentStats[] SortAgents(AgentStats[] agents)
    {
        if (sortByScore)
        {
            return agents.OrderByDescending(a => a.GetScore())
                        .ThenByDescending(a => a.GetKDR())
                        .ThenByDescending(a => a.kills)
                        .ToArray();
        }
        else
        {
            return agents.OrderByDescending(a => a.kills)
                        .ThenByDescending(a => a.GetKDR())
                        .ThenByDescending(a => a.revives)
                        .ToArray();
        }
    }

    void CreateAgentRow(AgentStats agent, int rank)
    {
        if (rowPrefab == null) return;

        GameObject row = Instantiate(rowPrefab, leaderboardContainer);

        // Set team color
        if (showTeamColors)
        {
            Image rowBackground = row.GetComponent<Image>();
            if (rowBackground != null)
            {
                Color teamColor = GetTeamColor(agent.teamName);
                teamColor.a = 0.3f; // Semi-transparent
                rowBackground.color = teamColor;
            }
        }

        // Populate row data
        SetRowText(row, "Rank", rank.ToString());
        SetRowText(row, "Name", agent.agentName);
        SetRowText(row, "Role", agent.role);
        SetRowText(row, "Score", agent.GetScore().ToString());
        SetRowText(row, "KDR", agent.GetFormattedKDR());
        SetRowText(row, "Kills", agent.kills.ToString());
        SetRowText(row, "Deaths", agent.deaths.ToString());
        SetRowText(row, "Assists", agent.assists.ToString());
        SetRowText(row, "Revives", agent.revives.ToString());
        SetRowText(row, "Zones", agent.zonesCaptures.ToString());
        SetRowText(row, "Accuracy", agent.GetFormattedAccuracy());
        SetRowText(row, "DPM", agent.GetFormattedDPM());
        SetRowText(row, "Status", agent.IsAlive ? "🟢 Alive" : "💀 KIA");
    }

    void SetRowText(GameObject row, string childName, string text)
    {
        Transform child = row.transform.Find(childName);
        if (child != null)
        {
            Text textComponent = child.GetComponent<Text>();
            if (textComponent != null)
            {
                textComponent.text = text;
            }
        }
    }

    Color GetTeamColor(string teamName)
    {
        if (teamName.ToLower().Contains("a") || teamName.ToLower().Contains("blue"))
            return teamAColor;
        else if (teamName.ToLower().Contains("b") || teamName.ToLower().Contains("red"))
            return teamBColor;
        else
            return neutralColor;
    }

    void CreateNoDataRow()
    {
        if (rowPrefab == null) return;

        GameObject row = Instantiate(rowPrefab, leaderboardContainer);
        SetRowText(row, "Name", "No agents found");
        SetRowText(row, "Score", "---");
        SetRowText(row, "KDR", "---");
        SetRowText(row, "Status", "Waiting for training...");
    }

    void UpdateRoundInfo(AgentStats[] agents)
    {
        if (roundInfoText == null) return;

        int aliveCount = agents.Count(a => a.IsAlive);
        int totalKills = agents.Sum(a => a.kills);
        int totalRevives = agents.Sum(a => a.revives);
        float avgAccuracy = agents.Average(a => a.accuracy);

        string info = $"Agents: {aliveCount}/{agents.Length} alive | " +
                     $"Total Kills: {totalKills} | " +
                     $"Total Revives: {totalRevives} | " +
                     $"Avg Accuracy: {avgAccuracy:F1}%";

        roundInfoText.text = info;
    }

    #region Public Methods
    /// <summary>
    /// Toggle between score-based and kill-based sorting
    /// </summary>
    public void ToggleSortMode()
    {
        sortByScore = !sortByScore;
        UpdateLeaderboard();
    }

    /// <summary>
    /// Toggle team color display
    /// </summary>
    public void ToggleTeamColors()
    {
        showTeamColors = !showTeamColors;
        UpdateLeaderboard();
    }

    /// <summary>
    /// Set refresh rate for leaderboard updates
    /// </summary>
    public void SetRefreshRate(float newRate)
    {
        refreshRate = Mathf.Clamp(newRate, 0.5f, 10f);
        CancelInvoke(nameof(UpdateLeaderboard));
        InvokeRepeating(nameof(UpdateLeaderboard), 1f, refreshRate);
    }

    /// <summary>
    /// Export current leaderboard data to CSV
    /// </summary>
    public void ExportToCSV()
    {
        AgentStats[] agents = FindObjectsOfType<AgentStats>();
        if (agents.Length == 0) return;

        string csv = "Agent,Team,Role,Score,Kills,Deaths,Assists,KDR,Revives,Zones,Accuracy,DPM,Status\n";

        foreach (var agent in SortAgents(agents))
        {
            csv += $"{agent.agentName},{agent.teamName},{agent.role},{agent.GetScore()}," +
                   $"{agent.kills},{agent.deaths},{agent.assists},{agent.GetKDR():F2}," +
                   $"{agent.revives},{agent.zonesCaptures},{agent.accuracy:F1},{agent.GetFormattedDPM()}," +
                   $"{(agent.IsAlive ? "Alive" : "KIA")}\n";
        }

        string filename = $"leaderboard_{System.DateTime.Now:yyyyMMdd_HHmmss}.csv";
        System.IO.File.WriteAllText(filename, csv);
        Debug.Log($"📊 Leaderboard exported to {filename}");
    }
    #endregion
}
