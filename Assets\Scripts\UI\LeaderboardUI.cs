using UnityEngine;
using System.Linq;
using System.Collections.Generic;

/// <summary>
/// Advanced leaderboard system with detailed analytics (console-based, no UI dependencies)
/// Use SimpleLeaderboardUI for basic functionality or this for detailed analysis
/// </summary>
public class LeaderboardUI : MonoBehaviour
{
    [Header("🏆 Advanced Analytics")]
    public bool enableDetailedAnalytics = true;
    public bool enablePerformanceTrends = true;

    [Header("⚙️ Settings")]
    public float refreshRate = 10f;
    public int maxDisplayedAgents = 15;
    public bool sortByScore = true;

    [Header("📈 Analytics")]
    public bool trackPerformanceTrends = true;
    public bool generateDetailedReports = true;

    private Dictionary<string, List<float>> performanceHistory = new Dictionary<string, List<float>>();
    private float lastAnalyticsTime = 0f;

    void Start()
    {
        Debug.Log("🏆 Advanced SquadMate AI Analytics Started");
        InvokeRepeating(nameof(UpdateLeaderboard), 3f, refreshRate);

        if (enableDetailedAnalytics)
        {
            InvokeRepeating(nameof(GenerateAnalyticsReport), 30f, 60f); // Every minute
        }
    }

    void UpdateLeaderboard()
    {
        AgentStats[] allAgents = FindObjectsOfType<AgentStats>();

        if (allAgents.Length == 0)
        {
            return; // No output if no agents
        }

        var sortedAgents = SortAgents(allAgents);

        if (enableDetailedAnalytics)
        {
            UpdatePerformanceHistory(allAgents);
        }

        // Generate and log detailed leaderboard
        string report = GenerateDetailedReport(sortedAgents, allAgents);
        Debug.Log(report);
    }

    AgentStats[] SortAgents(AgentStats[] agents)
    {
        if (sortByScore)
        {
            return agents.OrderByDescending(a => a.GetScore())
                        .ThenByDescending(a => a.GetKDR())
                        .ThenByDescending(a => a.kills)
                        .ToArray();
        }
        else
        {
            return agents.OrderByDescending(a => a.kills)
                        .ThenByDescending(a => a.GetKDR())
                        .ThenByDescending(a => a.revives)
                        .ToArray();
        }
    }

    string GenerateDetailedReport(AgentStats[] sortedAgents, AgentStats[] allAgents)
    {
        string report = "\n🏆 ===== ADVANCED SQUADMATE ANALYTICS ===== 🏆\n";

        // Overall statistics
        report += GenerateOverallStats(allAgents);

        // Team comparison
        report += GenerateTeamComparison(allAgents);

        // Individual rankings with detailed metrics
        report += GenerateDetailedRankings(sortedAgents);

        // Performance trends (if enabled)
        if (enablePerformanceTrends && performanceHistory.Count > 0)
        {
            report += GeneratePerformanceTrends();
        }

        report += "\n" + new string('=', 60);
        return report;
    }

    string GenerateOverallStats(AgentStats[] agents)
    {
        int totalKills = agents.Sum(a => a.kills);
        int totalDeaths = agents.Sum(a => a.deaths);
        int totalRevives = agents.Sum(a => a.revives);
        int totalZones = agents.Sum(a => a.zonesCaptures);
        float avgAccuracy = agents.Length > 0 ? agents.Average(a => a.accuracy) : 0f;
        int aliveCount = agents.Count(a => a.IsAlive);

        string stats = "\n📊 OVERALL STATISTICS:\n";
        stats += $"   Agents: {aliveCount}/{agents.Length} alive\n";
        stats += $"   Total Kills: {totalKills} | Total Deaths: {totalDeaths}\n";
        stats += $"   Total Revives: {totalRevives} | Zones Captured: {totalZones}\n";
        stats += $"   Average Accuracy: {avgAccuracy:F1}%\n";

        return stats;
    }

    string GenerateTeamComparison(AgentStats[] agents)
    {
        var teamA = agents.Where(a => a.teamName.ToLower().Contains("a")).ToArray();
        var teamB = agents.Where(a => a.teamName.ToLower().Contains("b")).ToArray();

        if (teamA.Length == 0 || teamB.Length == 0) return "";

        string comparison = "\n⚔️ TEAM COMPARISON:\n";

        // Team A stats
        int teamAKills = teamA.Sum(a => a.kills);
        int teamARevives = teamA.Sum(a => a.revives);
        int teamAZones = teamA.Sum(a => a.zonesCaptures);
        int teamAAlive = teamA.Count(a => a.IsAlive);
        float teamAAccuracy = teamA.Average(a => a.accuracy);

        // Team B stats
        int teamBKills = teamB.Sum(a => a.kills);
        int teamBRevives = teamB.Sum(a => a.revives);
        int teamBZones = teamB.Sum(a => a.zonesCaptures);
        int teamBAlive = teamB.Count(a => a.IsAlive);
        float teamBAccuracy = teamB.Average(a => a.accuracy);

        comparison += $"   🔵 TEAM A: {teamAAlive}/{teamA.Length} alive | ";
        comparison += $"Kills: {teamAKills} | Revives: {teamARevives} | Zones: {teamAZones} | Acc: {teamAAccuracy:F1}%\n";

        comparison += $"   🔴 TEAM B: {teamBAlive}/{teamB.Length} alive | ";
        comparison += $"Kills: {teamBKills} | Revives: {teamBRevives} | Zones: {teamBZones} | Acc: {teamBAccuracy:F1}%\n";

        return comparison;
    }

    string GenerateDetailedRankings(AgentStats[] sortedAgents)
    {
        string rankings = "\n🎯 DETAILED AGENT RANKINGS:\n";

        int displayCount = Mathf.Min(maxDisplayedAgents, sortedAgents.Length);
        for (int i = 0; i < displayCount; i++)
        {
            var agent = sortedAgents[i];
            string teamIcon = GetTeamIcon(agent.teamName);
            string status = agent.IsAlive ? "🟢" : "💀";

            rankings += $"\n{i + 1}. {teamIcon} {agent.agentName} ({agent.role}) {status}\n";
            rankings += $"   Score: {agent.GetScore()} | K/D/A: {agent.kills}/{agent.deaths}/{agent.assists} ({agent.GetFormattedKDR()})\n";
            rankings += $"   Combat: {agent.GetFormattedAccuracy()} accuracy | {agent.GetFormattedDPM()} DPM\n";
            rankings += $"   Support: {agent.revives} revives | {agent.medkitsUsed} medkits used\n";
            rankings += $"   Tactical: {agent.zonesCaptures} zones captured | {agent.timeAlive:F0}s alive\n";
        }

        return rankings;
    }

    string GetTeamIcon(string teamName)
    {
        if (teamName.ToLower().Contains("a") || teamName.ToLower().Contains("blue"))
            return "🔵";
        else if (teamName.ToLower().Contains("b") || teamName.ToLower().Contains("red"))
            return "🔴";
        else
            return "⚪";
    }

    void UpdatePerformanceHistory(AgentStats[] agents)
    {
        foreach (var agent in agents)
        {
            string key = agent.agentName;
            if (!performanceHistory.ContainsKey(key))
            {
                performanceHistory[key] = new List<float>();
            }

            // Track score over time
            performanceHistory[key].Add(agent.GetScore());

            // Keep only last 10 entries to avoid memory issues
            if (performanceHistory[key].Count > 10)
            {
                performanceHistory[key].RemoveAt(0);
            }
        }
    }

    string GeneratePerformanceTrends()
    {
        string trends = "\n📈 PERFORMANCE TRENDS:\n";

        foreach (var kvp in performanceHistory)
        {
            if (kvp.Value.Count < 2) continue;

            float firstScore = kvp.Value[0];
            float lastScore = kvp.Value[kvp.Value.Count - 1];
            float change = lastScore - firstScore;
            string trend = change > 0 ? "📈" : change < 0 ? "📉" : "➡️";

            trends += $"   {kvp.Key}: {trend} {change:+0;-0;0} (from {firstScore:F0} to {lastScore:F0})\n";
        }

        return trends;
    }

    void GenerateAnalyticsReport()
    {
        if (!generateDetailedReports) return;

        AgentStats[] agents = FindObjectsOfType<AgentStats>();
        if (agents.Length == 0) return;

        string report = "\n📊 ===== PERIODIC ANALYTICS REPORT ===== 📊\n";
        report += $"Time: {System.DateTime.Now:HH:mm:ss}\n";

        // Find top performers in different categories
        var topKiller = agents.OrderByDescending(a => a.kills).FirstOrDefault();
        var topSupporter = agents.OrderByDescending(a => a.revives).FirstOrDefault();
        var topTactical = agents.OrderByDescending(a => a.zonesCaptures).FirstOrDefault();
        var mostAccurate = agents.OrderByDescending(a => a.accuracy).FirstOrDefault();

        report += "\n🏅 CATEGORY LEADERS:\n";
        if (topKiller != null) report += $"   🔫 Top Killer: {topKiller.agentName} ({topKiller.kills} kills)\n";
        if (topSupporter != null) report += $"   🛟 Top Supporter: {topSupporter.agentName} ({topSupporter.revives} revives)\n";
        if (topTactical != null) report += $"   🎯 Top Tactical: {topTactical.agentName} ({topTactical.zonesCaptures} zones)\n";
        if (mostAccurate != null) report += $"   🎯 Most Accurate: {mostAccurate.agentName} ({mostAccurate.GetFormattedAccuracy()})\n";

        report += "\n" + new string('=', 50);
        Debug.Log(report);
    }

    #region Public Methods
    [ContextMenu("Toggle Sort Mode")]
    public void ToggleSortMode()
    {
        sortByScore = !sortByScore;
        string mode = sortByScore ? "Score" : "Kills";
        Debug.Log($"📊 Advanced analytics sort mode: {mode}");
        UpdateLeaderboard();
    }

    [ContextMenu("Generate Immediate Report")]
    public void GenerateImmediateReport()
    {
        UpdateLeaderboard();
    }

    [ContextMenu("Export Detailed CSV")]
    public void ExportToCSV()
    {
        AgentStats[] agents = FindObjectsOfType<AgentStats>();
        if (agents.Length == 0)
        {
            Debug.LogWarning("No agents found for export");
            return;
        }

        string csv = "Agent,Team,Role,Score,Kills,Deaths,Assists,KDR,Revives,Zones,Accuracy,DPM,TimeAlive,Status\n";

        foreach (var agent in SortAgents(agents))
        {
            csv += $"{agent.agentName},{agent.teamName},{agent.role},{agent.GetScore()}," +
                   $"{agent.kills},{agent.deaths},{agent.assists},{agent.GetKDR():F2}," +
                   $"{agent.revives},{agent.zonesCaptures},{agent.accuracy:F1},{agent.GetFormattedDPM()}," +
                   $"{agent.timeAlive:F0},{(agent.IsAlive ? "Alive" : "KIA")}\n";
        }

        string filename = $"detailed_analytics_{System.DateTime.Now:yyyyMMdd_HHmmss}.csv";
        System.IO.File.WriteAllText(filename, csv);
        Debug.Log($"📊 Detailed analytics exported to {filename}");
    }

    [ContextMenu("Clear Performance History")]
    public void ClearPerformanceHistory()
    {
        performanceHistory.Clear();
        Debug.Log("📊 Performance history cleared");
    }

    public void SetRefreshRate(float newRate)
    {
        refreshRate = Mathf.Clamp(newRate, 5f, 60f);
        CancelInvoke(nameof(UpdateLeaderboard));
        InvokeRepeating(nameof(UpdateLeaderboard), 3f, refreshRate);
        Debug.Log($"📊 Advanced analytics refresh rate: {refreshRate}s");
    }
    #endregion
}
