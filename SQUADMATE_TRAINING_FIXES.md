# 🔧 SquadMate AI Training Fixes & Optimization Guide

## 🚀 Quick Start - One-Click Fix

**Use the new Training Optimizer:**
```
Unity Editor → SquadMate AI → 🚀 Training Optimizer → 🔧 Fix All Common Issues
```

This will automatically fix all common issues and optimize your training setup.

## 🔍 Common Issues & Solutions

### 1. **Missing Component Errors**

**Problem:** `NullReferenceException` or missing component warnings

**Solution:**
```csharp
// Use the Training Optimizer or manually add:
- PUBGInventory
- PUBGArmorSystem  
- WeaponSystem
- HealthSystem
- DynamicRewardSystem
- Rigidbody (with proper constraints)
```

### 2. **PUBG Item Database Not Found**

**Problem:** `itemDatabase is null` errors

**Solution:**
```csharp
// Auto-fix via Training Optimizer or:
1. Create PUBGItemDatabase asset
2. Initialize with default items
3. Assign to PUBGItemSystem
```

### 3. **Agent Observations Issues**

**Problem:** Agent not learning or poor performance

**Solution:**
- Ensure all observation references are set
- Validate environment and itemSystem references
- Check observation space size (should be ~34 observations)

### 4. **Reward System Problems**

**Problem:** Rewards too sparse or unbalanced

**Solution:**
```yaml
# Optimized reward values:
- Loot Pickup: 0.3
- Weapon Pickup: 0.5  
- Healing: 0.4
- Combat: 0.2
- Survival: 0.1
```

### 5. **Training Performance Issues**

**Problem:** Slow training or low FPS

**Solution:**
```csharp
// Auto-optimized via Training Optimizer:
- Disable VSync
- Reduce shadow quality
- Optimize physics iterations
- Use time_scale: 20 in config
```

## 🎯 Training Configuration Optimization

### **Unity 6 Optimized Config**
```yaml
behaviors:
  SquadMate:
    trainer_type: ppo
    hyperparameters:
      batch_size: 4096
      buffer_size: 40960
      learning_rate: 2.5e-4
      beta: 3.0e-3
      epsilon: 0.15
      lambd: 0.97
      num_epoch: 4

    network_settings:
      normalize: true
      hidden_units: 768
      num_layers: 4
      memory:
        sequence_length: 128
        memory_size: 512

    max_steps: 8000000
    time_horizon: 512
    summary_freq: 2500
```

### **Advanced Features**
```yaml
unity6_optimizations:
  use_burst_compilation: true
  enable_job_system: true
  batch_observations: true
  parallel_environments: true

curriculum_learning:
  enabled: true
  thresholds:
    basic_movement: 0.3
    loot_collection: 0.6
    weapon_usage: 1.0
    healing_strategy: 1.2
    combat_engagement: 1.5
    tactical_positioning: 2.0
```

## 🧪 Validation & Testing

### **System Validation Checklist**
- ✅ SquadMateAgent in scene
- ✅ PUBGItemSystem with database
- ✅ GameEnvironment configured
- ✅ All agent components present
- ✅ Proper observation space
- ✅ Reward system optimized

### **Testing Commands**
```csharp
// In Unity Editor:
SquadMate AI → 🧪 Test PUBG Systems
SquadMate AI → 🚀 Training Optimizer → 🧪 Validate PUBG Systems

// Debug commands:
agent.inventory.DebugPrintInventory();
itemSystem.SpawnRandomLoot();
Debug.Log($"Combat: {agent.isInCombat}, Health: {agent.currentHealth}");
```

## 🎮 Training Best Practices

### **1. Progressive Training**
```
Phase 1: Basic movement & loot collection (0-500k steps)
Phase 2: Weapon usage & combat basics (500k-2M steps)  
Phase 3: Advanced tactics & healing (2M-5M steps)
Phase 4: Team coordination & strategy (5M+ steps)
```

### **2. Monitoring Training**
```bash
# Watch training progress:
tensorboard --logdir results

# Key metrics to monitor:
- Cumulative Reward (should increase)
- Episode Length (should stabilize)
- Policy Loss (should decrease)
- Value Loss (should decrease)
```

### **3. Curriculum Learning**
```yaml
# Automatic difficulty progression:
- Start with simple scenarios
- Gradually add enemies
- Increase loot scarcity
- Add tactical complexity
```

## 🔧 Advanced Troubleshooting

### **Memory Issues**
```yaml
# Reduce memory usage:
buffer_size: 20480  # Smaller buffer
batch_size: 2048    # Smaller batches
num_envs: 1         # Single environment
```

### **Convergence Problems**
```yaml
# Improve learning stability:
learning_rate: 1e-4     # Lower learning rate
beta: 1e-3              # Lower entropy
epsilon: 0.1            # Tighter policy updates
```

### **Performance Optimization**
```csharp
// Unity settings for training:
QualitySettings.vSyncCount = 0;
QualitySettings.shadows = ShadowQuality.Disable;
Physics.defaultSolverIterations = 4;
Time.timeScale = 20f;
```

## 📊 Expected Training Results

### **After 2 Hours (1M steps):**
- ✅ Basic movement and following
- ✅ Simple loot collection
- ✅ Basic weapon pickup

### **After 6 Hours (3M steps):**
- ✅ Combat engagement
- ✅ Tactical healing
- ✅ Cover usage

### **After 12+ Hours (6M+ steps):**
- ✅ Advanced tactics
- ✅ Team coordination
- ✅ Strategic decision making

## 🆘 Emergency Fixes

### **Training Won't Start**
```bash
1. Check Python/ML-Agents installation
2. Verify config file syntax
3. Ensure Unity scene is properly set up
4. Run Training Optimizer
```

### **Agent Not Learning**
```bash
1. Check reward signals in TensorBoard
2. Validate observation space
3. Ensure proper episode termination
4. Optimize hyperparameters
```

### **Performance Issues**
```bash
1. Reduce visual quality
2. Optimize physics settings
3. Use smaller batch sizes
4. Enable Unity 6 optimizations
```

## 🎯 Success Indicators

**Your SquadMate AI is training successfully when:**
- ✅ Cumulative reward increases over time
- ✅ Agent actively seeks and collects loot
- ✅ Tactical healing behavior emerges
- ✅ Combat engagement improves
- ✅ Team coordination develops

---

**🎉 With these fixes and optimizations, your SquadMate AI should train effectively and develop sophisticated PUBG-style behaviors!**
