behaviors:
  SquadMate:
    trainer_type: ppo
    hyperparameters:
      batch_size: 1024
      buffer_size: 10240
      learning_rate: 3.0e-4
      beta: 5.0e-3
      epsilon: 0.2
      lambd: 0.95
      num_epoch: 3
      learning_rate_schedule: linear

    network_settings:
      normalize: false
      hidden_units: 256
      num_layers: 2
      vis_encode_type: simple

    reward_signals:
      extrinsic:
        gamma: 0.99
        strength: 1.0

    max_steps: 2000000
    time_horizon: 128
    summary_freq: 10000
    threaded: true

env_settings:
  env_path: null
  env_args: null
  base_port: 5005
  num_envs: 1
  seed: -1

engine_settings:
  width: 1280
  height: 720
  quality_level: 0
  time_scale: 10
  target_frame_rate: 60
  capture_frame_rate: 60
  no_graphics: false

checkpoint_settings:
  run_id: simple_pubg_training
  initialize_from: null
  load_model: false
  resume: false
  force: false
  train_model: true
  inference: false
  results_dir: results
