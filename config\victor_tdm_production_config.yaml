behaviors:
  VictorTDM:
    trainer_type: poca
    hyperparameters:
      batch_size: 2048
      buffer_size: 20480
      learning_rate: 3e-4
      beta: 5e-3
      epsilon: 0.2
      lambd: 0.95
      num_epoch: 3
      learning_rate_schedule: linear

    network_settings:
      normalize: true
      hidden_units: 512
      num_layers: 3
      vis_encode_type: simple
      memory:
        sequence_length: 64
        memory_size: 256

    reward_signals:
      extrinsic:
        gamma: 0.99
        strength: 1.0
      curiosity:
        gamma: 0.99
        strength: 0.02
        encoding_size: 256
        learning_rate: 1e-3

    max_steps: 5000000
    time_horizon: 1000
    summary_freq: 10000
    threaded: true

    self_play:
      save_steps: 50000
      team_change: 200000
      swap_steps: 10000
      play_against_latest_model_ratio: 0.5
      window: 10

    behavioral_cloning:
      demo_path: null
      strength: 0.5
      steps: 0

env_settings:
  env_path: null
  env_args: null
  base_port: 5005
  num_envs: 1
  seed: -1

engine_settings:
  width: 1280
  height: 720
  quality_level: 1
  time_scale: 20
  target_frame_rate: 60
  capture_frame_rate: 60
  no_graphics: false

checkpoint_settings:
  run_id: victor_tdm_production
  initialize_from: null
  load_model: false
  resume: false
  force: false
  train_model: true
  inference: false
  results_dir: results

# Production-specific settings
production_settings:
  # Role-based training
  role_training:
    enabled: true
    support_weight: 1.2
    assault_weight: 1.0
    scout_weight: 1.1
    anchor_weight: 1.0
  
  # Zone control training
  zone_training:
    enabled: true
    capture_reward_multiplier: 1.5
    defense_reward_multiplier: 1.2
    contest_penalty_multiplier: 0.8
  
  # Team coordination training
  team_training:
    enabled: true
    coordination_reward_multiplier: 1.3
    communication_reward_multiplier: 1.1
    formation_reward_multiplier: 1.0
  
  # Decision tree integration
  decision_tree:
    enabled: true
    hot_reload: true
    update_interval: 2.0
    fallback_behavior: "patrol_nearest_zone"
  
  # Performance monitoring
  monitoring:
    enabled: true
    log_interval: 1000
    save_performance_data: true
    track_role_effectiveness: true
    track_zone_control: true
    track_team_coordination: true

# Advanced curriculum learning
curriculum:
  enabled: true
  
  # Phase 1: Basic behaviors (0-500k steps)
  phase_1:
    max_steps: 500000
    objectives:
      - basic_movement
      - weapon_pickup
      - simple_combat
      - role_recognition
    rewards:
      movement: 1.0
      weapon_pickup: 1.5
      combat: 1.2
      role_behavior: 1.3
  
  # Phase 2: Team coordination (500k-2M steps)
  phase_2:
    max_steps: 2000000
    objectives:
      - squad_formation
      - zone_awareness
      - basic_tactics
      - role_specialization
    rewards:
      team_proximity: 1.2
      zone_control: 1.4
      tactical_movement: 1.3
      role_performance: 1.5
  
  # Phase 3: Advanced tactics (2M+ steps)
  phase_3:
    max_steps: 5000000
    objectives:
      - complex_strategies
      - adaptive_roles
      - advanced_coordination
      - competitive_play
    rewards:
      strategic_thinking: 1.5
      role_adaptation: 1.4
      team_synergy: 1.6
      competitive_performance: 1.3

# Quality assurance
quality_assurance:
  # Validation checks
  validation:
    check_role_distribution: true
    check_zone_coverage: true
    check_team_balance: true
    check_performance_metrics: true
  
  # Performance thresholds
  thresholds:
    min_role_effectiveness: 0.6
    min_zone_control_rate: 0.4
    min_team_coordination: 0.5
    min_win_rate: 0.45
  
  # Auto-adjustments
  auto_adjust:
    enabled: true
    learning_rate_adjustment: true
    reward_balancing: true
    curriculum_pacing: true

# Debugging and development
debug:
  enabled: false
  verbose_logging: false
  save_decision_logs: false
  track_individual_agents: false
  export_behavior_trees: false
