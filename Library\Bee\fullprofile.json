{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 8368, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 8368, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 8368, "tid": 706, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 8368, "tid": 706, "ts": 1749748928491088, "dur": 1043, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 8368, "tid": 706, "ts": 1749748928497089, "dur": 1207, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 8368, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 8368, "tid": 1, "ts": ***********11920, "dur": 7428, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 8368, "tid": 1, "ts": ***********19353, "dur": 44425, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 8368, "tid": 1, "ts": ***********63791, "dur": 22357, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 8368, "tid": 706, "ts": 1749748928498302, "dur": 14, "ph": "X", "name": "", "args": {}}, {"pid": 8368, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 8368, "tid": 12884901888, "ts": ***********09426, "dur": 12814, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********22243, "dur": 2251426, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********24218, "dur": 3716, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********27942, "dur": 2631, "ph": "X", "name": "ProcessMessages 19899", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********30578, "dur": 523, "ph": "X", "name": "ReadAsync 19899", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********31106, "dur": 24, "ph": "X", "name": "ProcessMessages 20543", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********31131, "dur": 115, "ph": "X", "name": "ReadAsync 20543", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********31251, "dur": 2, "ph": "X", "name": "ProcessMessages 727", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********31254, "dur": 89, "ph": "X", "name": "ReadAsync 727", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********31348, "dur": 2, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********31351, "dur": 100, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********31454, "dur": 2, "ph": "X", "name": "ProcessMessages 716", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********31458, "dur": 96, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********31558, "dur": 2, "ph": "X", "name": "ProcessMessages 536", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********31561, "dur": 56, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********31620, "dur": 2, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********31624, "dur": 49, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********31676, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********31679, "dur": 42, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********31724, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********31728, "dur": 46, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********31777, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********31780, "dur": 39, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********31822, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********31824, "dur": 36, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********31863, "dur": 1, "ph": "X", "name": "ProcessMessages 119", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********31865, "dur": 51, "ph": "X", "name": "ReadAsync 119", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********31920, "dur": 1, "ph": "X", "name": "ProcessMessages 207", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********31922, "dur": 82, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********32008, "dur": 1, "ph": "X", "name": "ProcessMessages 234", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********32010, "dur": 53, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********32068, "dur": 2, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********32072, "dur": 111, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********32187, "dur": 1, "ph": "X", "name": "ProcessMessages 238", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********32189, "dur": 53, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********32247, "dur": 2, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********32250, "dur": 41, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********32298, "dur": 44, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********32345, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********32347, "dur": 602, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********32958, "dur": 2, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********32962, "dur": 166, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********33132, "dur": 5, "ph": "X", "name": "ProcessMessages 3502", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********33138, "dur": 53, "ph": "X", "name": "ReadAsync 3502", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********33195, "dur": 2, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********33198, "dur": 42, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********33243, "dur": 1, "ph": "X", "name": "ProcessMessages 158", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********33246, "dur": 44, "ph": "X", "name": "ReadAsync 158", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********33294, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********33296, "dur": 48, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********33347, "dur": 1, "ph": "X", "name": "ProcessMessages 231", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********33350, "dur": 44, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********33399, "dur": 1, "ph": "X", "name": "ProcessMessages 198", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********33402, "dur": 47, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********33453, "dur": 1, "ph": "X", "name": "ProcessMessages 202", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********33454, "dur": 52, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********33510, "dur": 1, "ph": "X", "name": "ProcessMessages 261", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********33512, "dur": 43, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********33561, "dur": 4, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********33568, "dur": 91, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********33662, "dur": 1, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********33664, "dur": 51, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********33719, "dur": 2, "ph": "X", "name": "ProcessMessages 771", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********33724, "dur": 47, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********33774, "dur": 1, "ph": "X", "name": "ProcessMessages 169", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********33776, "dur": 44, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********33823, "dur": 1, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********33826, "dur": 44, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********33877, "dur": 4, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********33884, "dur": 93, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********33981, "dur": 1, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********33984, "dur": 52, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********34040, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********34042, "dur": 44, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********34090, "dur": 1, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********34092, "dur": 39, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********34134, "dur": 1, "ph": "X", "name": "ProcessMessages 133", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********34137, "dur": 39, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********34179, "dur": 1, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********34181, "dur": 38, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********34223, "dur": 1, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********34225, "dur": 72, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********34301, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********34304, "dur": 44, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********34351, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********34354, "dur": 35, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********34392, "dur": 1, "ph": "X", "name": "ProcessMessages 74", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********34394, "dur": 152, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********34550, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********34552, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********34602, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********34607, "dur": 35, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********34646, "dur": 1, "ph": "X", "name": "ProcessMessages 157", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********34648, "dur": 39, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********34690, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********34692, "dur": 38, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********34734, "dur": 1, "ph": "X", "name": "ProcessMessages 187", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********34736, "dur": 38, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********34777, "dur": 1, "ph": "X", "name": "ProcessMessages 214", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********34779, "dur": 37, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********34819, "dur": 1, "ph": "X", "name": "ProcessMessages 231", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********34821, "dur": 35, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********34860, "dur": 1, "ph": "X", "name": "ProcessMessages 170", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********34862, "dur": 36, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********34901, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********34904, "dur": 39, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********34948, "dur": 38, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********34989, "dur": 1, "ph": "X", "name": "ProcessMessages 247", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********34991, "dur": 44, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********35039, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********35041, "dur": 45, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********35093, "dur": 4, "ph": "X", "name": "ProcessMessages 279", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********35100, "dur": 109, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********35213, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********35215, "dur": 49, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********35272, "dur": 2, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********35275, "dur": 110, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********35389, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********35392, "dur": 53, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********35449, "dur": 2, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********35452, "dur": 118, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********35574, "dur": 1, "ph": "X", "name": "ProcessMessages 276", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********35577, "dur": 83, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********35664, "dur": 2, "ph": "X", "name": "ProcessMessages 724", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********35667, "dur": 50, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********35721, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********35724, "dur": 46, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********35773, "dur": 1, "ph": "X", "name": "ProcessMessages 275", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********35775, "dur": 44, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********35823, "dur": 3, "ph": "X", "name": "ProcessMessages 223", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********35828, "dur": 44, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********35878, "dur": 4, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********35884, "dur": 94, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********35982, "dur": 1, "ph": "X", "name": "ProcessMessages 281", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********35986, "dur": 49, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********36038, "dur": 2, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********36041, "dur": 41, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********36086, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********36088, "dur": 50, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********36141, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********36145, "dur": 42, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********36193, "dur": 1, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********36196, "dur": 95, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********36294, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********36296, "dur": 78, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********36378, "dur": 2, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********36381, "dur": 70, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********36455, "dur": 1, "ph": "X", "name": "ProcessMessages 408", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********36458, "dur": 45, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********36506, "dur": 1, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********36508, "dur": 62, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********36574, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********36576, "dur": 45, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********36625, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********36627, "dur": 36, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********36667, "dur": 1, "ph": "X", "name": "ProcessMessages 191", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********36669, "dur": 38, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********36710, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********36712, "dur": 36, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********36752, "dur": 1, "ph": "X", "name": "ProcessMessages 218", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********36754, "dur": 38, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********36795, "dur": 1, "ph": "X", "name": "ProcessMessages 189", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********36797, "dur": 38, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********36838, "dur": 1, "ph": "X", "name": "ProcessMessages 170", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********36841, "dur": 39, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********36882, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********36886, "dur": 34, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********36923, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********36925, "dur": 42, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********36973, "dur": 1, "ph": "X", "name": "ProcessMessages 234", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********36976, "dur": 45, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********37027, "dur": 1, "ph": "X", "name": "ProcessMessages 79", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********37031, "dur": 41, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********37075, "dur": 1, "ph": "X", "name": "ProcessMessages 203", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********37078, "dur": 38, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********37119, "dur": 1, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********37121, "dur": 42, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********37167, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********37169, "dur": 37, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********37212, "dur": 35, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********37250, "dur": 1, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********37252, "dur": 37, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********37293, "dur": 1, "ph": "X", "name": "ProcessMessages 175", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********37295, "dur": 38, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********37336, "dur": 1, "ph": "X", "name": "ProcessMessages 154", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********37338, "dur": 39, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********37381, "dur": 1, "ph": "X", "name": "ProcessMessages 235", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********37382, "dur": 37, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********37423, "dur": 1, "ph": "X", "name": "ProcessMessages 226", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********37425, "dur": 42, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********37472, "dur": 1, "ph": "X", "name": "ProcessMessages 72", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********37474, "dur": 35, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********37514, "dur": 40, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********37558, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********37560, "dur": 35, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********37598, "dur": 1, "ph": "X", "name": "ProcessMessages 134", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********37600, "dur": 36, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********37640, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********37641, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********37684, "dur": 1, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********37687, "dur": 102, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********37792, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********37794, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********37838, "dur": 1, "ph": "X", "name": "ProcessMessages 191", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********37841, "dur": 40, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********37884, "dur": 1, "ph": "X", "name": "ProcessMessages 275", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********37886, "dur": 38, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********37928, "dur": 1, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********37930, "dur": 33, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********37966, "dur": 1, "ph": "X", "name": "ProcessMessages 78", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********37968, "dur": 89, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********38061, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********38063, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********38104, "dur": 1, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********38106, "dur": 132, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********38243, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********38287, "dur": 1, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********38289, "dur": 39, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********38332, "dur": 1, "ph": "X", "name": "ProcessMessages 201", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********38334, "dur": 33, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********38371, "dur": 1, "ph": "X", "name": "ProcessMessages 136", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********38373, "dur": 38, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********38414, "dur": 1, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********38417, "dur": 37, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********38457, "dur": 1, "ph": "X", "name": "ProcessMessages 189", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********38459, "dur": 29, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********38492, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********38494, "dur": 97, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********38597, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********38637, "dur": 1, "ph": "X", "name": "ProcessMessages 209", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********38639, "dur": 38, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********38680, "dur": 1, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********38683, "dur": 39, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********38725, "dur": 1, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********38727, "dur": 38, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********38769, "dur": 4, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********38774, "dur": 110, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********38888, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********38890, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********38931, "dur": 1, "ph": "X", "name": "ProcessMessages 251", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********38934, "dur": 37, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********38976, "dur": 34, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********39013, "dur": 1, "ph": "X", "name": "ProcessMessages 166", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********39016, "dur": 40, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********39059, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********39061, "dur": 36, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********39100, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********39103, "dur": 114, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********39222, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********39262, "dur": 1, "ph": "X", "name": "ProcessMessages 203", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********39264, "dur": 40, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********39308, "dur": 1, "ph": "X", "name": "ProcessMessages 227", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********39310, "dur": 38, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********39351, "dur": 1, "ph": "X", "name": "ProcessMessages 171", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********39353, "dur": 38, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********39395, "dur": 1, "ph": "X", "name": "ProcessMessages 157", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********39397, "dur": 35, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********39435, "dur": 1, "ph": "X", "name": "ProcessMessages 123", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********39438, "dur": 35, "ph": "X", "name": "ReadAsync 123", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********39476, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********39478, "dur": 38, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********39519, "dur": 1, "ph": "X", "name": "ProcessMessages 241", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********39522, "dur": 39, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********39565, "dur": 1, "ph": "X", "name": "ProcessMessages 220", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********39567, "dur": 33, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********39603, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********39605, "dur": 111, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********39721, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********39762, "dur": 1, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********39765, "dur": 40, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********39808, "dur": 1, "ph": "X", "name": "ProcessMessages 242", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********39810, "dur": 39, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********39852, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********39855, "dur": 39, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********39897, "dur": 1, "ph": "X", "name": "ProcessMessages 185", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********39899, "dur": 35, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********39937, "dur": 1, "ph": "X", "name": "ProcessMessages 63", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********39940, "dur": 137, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********40079, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********40081, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********40124, "dur": 1, "ph": "X", "name": "ProcessMessages 230", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********40126, "dur": 38, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********40167, "dur": 1, "ph": "X", "name": "ProcessMessages 199", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********40169, "dur": 39, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********40211, "dur": 1, "ph": "X", "name": "ProcessMessages 203", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********40213, "dur": 38, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********40254, "dur": 1, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********40257, "dur": 36, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********40296, "dur": 1, "ph": "X", "name": "ProcessMessages 146", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********40299, "dur": 128, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********40431, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********40476, "dur": 1, "ph": "X", "name": "ProcessMessages 251", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********40479, "dur": 39, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********40522, "dur": 1, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********40523, "dur": 35, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********40563, "dur": 1, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********40566, "dur": 38, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********40607, "dur": 1, "ph": "X", "name": "ProcessMessages 231", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********40609, "dur": 35, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********40647, "dur": 1, "ph": "X", "name": "ProcessMessages 138", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********40650, "dur": 115, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********40769, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********40812, "dur": 1, "ph": "X", "name": "ProcessMessages 233", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********40815, "dur": 37, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********40855, "dur": 1, "ph": "X", "name": "ProcessMessages 218", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********40857, "dur": 30, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********40890, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********40892, "dur": 37, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********40932, "dur": 1, "ph": "X", "name": "ProcessMessages 230", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********40934, "dur": 42, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********40979, "dur": 1, "ph": "X", "name": "ProcessMessages 230", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********40982, "dur": 34, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********41019, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********41021, "dur": 136, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********41162, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********41208, "dur": 1, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********41210, "dur": 36, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********41250, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********41252, "dur": 36, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********41291, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********41294, "dur": 38, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********41334, "dur": 1, "ph": "X", "name": "ProcessMessages 232", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********41336, "dur": 33, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********41372, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********41374, "dur": 101, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********41480, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********41522, "dur": 1, "ph": "X", "name": "ProcessMessages 210", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********41524, "dur": 39, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********41565, "dur": 1, "ph": "X", "name": "ProcessMessages 186", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********41568, "dur": 35, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********41606, "dur": 1, "ph": "X", "name": "ProcessMessages 182", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********41608, "dur": 45, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********41658, "dur": 1, "ph": "X", "name": "ProcessMessages 279", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********41661, "dur": 43, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********41707, "dur": 1, "ph": "X", "name": "ProcessMessages 200", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********41710, "dur": 134, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********41848, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********41850, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********41902, "dur": 1, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********41904, "dur": 36, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********41944, "dur": 1, "ph": "X", "name": "ProcessMessages 239", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********41946, "dur": 53, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********42002, "dur": 1, "ph": "X", "name": "ProcessMessages 293", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********42005, "dur": 34, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********42042, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********42045, "dur": 156, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********42207, "dur": 95, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********42305, "dur": 2, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********42308, "dur": 74, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********42387, "dur": 1, "ph": "X", "name": "ProcessMessages 232", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********42389, "dur": 40, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********42433, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********42435, "dur": 122, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********42562, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********42564, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********42614, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********42616, "dur": 34, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********42653, "dur": 1, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********42655, "dur": 39, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********42697, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********42699, "dur": 34, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********42736, "dur": 1, "ph": "X", "name": "ProcessMessages 141", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********42739, "dur": 142, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********42886, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********42929, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********42931, "dur": 45, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********42979, "dur": 1, "ph": "X", "name": "ProcessMessages 195", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********42982, "dur": 39, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********43025, "dur": 1, "ph": "X", "name": "ProcessMessages 221", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********43027, "dur": 35, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********43065, "dur": 1, "ph": "X", "name": "ProcessMessages 123", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********43067, "dur": 38, "ph": "X", "name": "ReadAsync 123", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********43109, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********43111, "dur": 37, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********43152, "dur": 1, "ph": "X", "name": "ProcessMessages 226", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********43154, "dur": 34, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********43193, "dur": 1, "ph": "X", "name": "ProcessMessages 175", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********43195, "dur": 43, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********43241, "dur": 1, "ph": "X", "name": "ProcessMessages 181", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********43244, "dur": 38, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********43285, "dur": 1, "ph": "X", "name": "ProcessMessages 194", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********43288, "dur": 44, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********43335, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********43337, "dur": 39, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********43379, "dur": 1, "ph": "X", "name": "ProcessMessages 222", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********43381, "dur": 34, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********43418, "dur": 1, "ph": "X", "name": "ProcessMessages 63", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********43421, "dur": 34, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********43460, "dur": 73, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********43538, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********43540, "dur": 106, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********43649, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********43651, "dur": 83, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********43738, "dur": 1, "ph": "X", "name": "ProcessMessages 203", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********43742, "dur": 51, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********43796, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********43799, "dur": 39, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********43842, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********43844, "dur": 39, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********43888, "dur": 1, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********43891, "dur": 38, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********43932, "dur": 1, "ph": "X", "name": "ProcessMessages 195", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********43934, "dur": 45, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********43983, "dur": 1, "ph": "X", "name": "ProcessMessages 185", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********43985, "dur": 39, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********44028, "dur": 1, "ph": "X", "name": "ProcessMessages 215", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********44030, "dur": 38, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********44071, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********44073, "dur": 38, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********44115, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********44117, "dur": 40, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********44160, "dur": 1, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********44162, "dur": 37, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********44203, "dur": 1, "ph": "X", "name": "ProcessMessages 197", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********44205, "dur": 144, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********44353, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********44355, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********44407, "dur": 2, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********44411, "dur": 135, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********44551, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********44554, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********44596, "dur": 513, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********45114, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********45174, "dur": 6, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********45183, "dur": 42, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********45229, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********45234, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********45273, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********45278, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********45315, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********45318, "dur": 53, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********45375, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********45378, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********45419, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********45424, "dur": 39, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********45466, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********45470, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********45509, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********45513, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********45554, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********45558, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********45595, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********45601, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********45632, "dur": 2, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********45637, "dur": 32, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********45672, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********45677, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********45715, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********45720, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********45774, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********45778, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********45821, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********45828, "dur": 31, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********45863, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********45867, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********45927, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********45931, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********45968, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********45973, "dur": 33, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********46011, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********46016, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********46056, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********46061, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********46107, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********46111, "dur": 30, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********46145, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********46150, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********46189, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********46194, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********46236, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********46241, "dur": 194, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********46438, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********46444, "dur": 53, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********46501, "dur": 5, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********46508, "dur": 39, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********46552, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********46556, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********46606, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********46610, "dur": 57, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********46672, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********46676, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********46713, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********46717, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********46765, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********46768, "dur": 121, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********46894, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********46900, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********46947, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********46952, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********46996, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********47000, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********47037, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********47041, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********47079, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********47084, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********47122, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********47127, "dur": 37, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********47168, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********47173, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********47215, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********47219, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********47260, "dur": 2, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********47264, "dur": 37, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********47306, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********47311, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********47347, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********47350, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********47391, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********47396, "dur": 31, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********47431, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********47435, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********47478, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********47483, "dur": 41, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********47530, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********47535, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********47578, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********47582, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********47623, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********47627, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********47669, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********47673, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********47713, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********47716, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********47754, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********47760, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********47799, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********47803, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********47831, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********47834, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********47867, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********47872, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********47910, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********47914, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********47954, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********47958, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48001, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48005, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48046, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48052, "dur": 39, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48096, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48100, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48139, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48145, "dur": 47, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48195, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48198, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48239, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48244, "dur": 28, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48274, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48281, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48312, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48316, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48354, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48357, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48395, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48399, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48440, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48445, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48486, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48491, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48529, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48533, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48571, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48575, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48617, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48622, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48664, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48667, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48700, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48703, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48732, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48735, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48766, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48768, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48798, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48800, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48830, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48834, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48880, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48882, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48920, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48924, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48963, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********48969, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********49007, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********49010, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********49040, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********49043, "dur": 27, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********49072, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********49075, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********49107, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********49109, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********49156, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********49193, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********49196, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********49234, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********49239, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********49279, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********49285, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********49329, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********49333, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********49372, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********49377, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********49421, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********49426, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********49466, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********49471, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********49512, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********49517, "dur": 79, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********49600, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********49604, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********49653, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********49659, "dur": 46, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********49709, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********49713, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********49748, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********49753, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********49794, "dur": 2, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********49799, "dur": 33, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********49835, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********49840, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********49874, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********49880, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********49918, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********49921, "dur": 31, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********49957, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********49965, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********50004, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********50009, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********50045, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********50048, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********50085, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********50089, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********50126, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********50129, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********50170, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********50175, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********50214, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********50219, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********50256, "dur": 4, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********50263, "dur": 65, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********50333, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********50336, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********50378, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********50384, "dur": 53, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********50441, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********50445, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********50483, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********50488, "dur": 152, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********50645, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********50649, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********50686, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********50689, "dur": 9346, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********60043, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********60047, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********60098, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********60101, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********60130, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********60133, "dur": 387, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********60525, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********60528, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********60567, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********60571, "dur": 356, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********60933, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********60937, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********60975, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********60980, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********61021, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********61024, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********61078, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********61084, "dur": 1405, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********62494, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********62499, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********62561, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********62566, "dur": 235, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********62805, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********62808, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********62845, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********62848, "dur": 145, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********62998, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********63002, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********63037, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********63039, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********63101, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********63104, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********63138, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********63141, "dur": 136, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********63282, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********63285, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********63320, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********63324, "dur": 401, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********63730, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********63734, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********63753, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********63755, "dur": 628, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********64387, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********64391, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********64419, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********64423, "dur": 96, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********64523, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********64526, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********64572, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********64575, "dur": 80, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********64660, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********64695, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********64700, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********64744, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********64747, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********64798, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********64801, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********64868, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********64871, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********64907, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********64912, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********64952, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********64954, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********65007, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********65014, "dur": 47, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********65067, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********65077, "dur": 614, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********65698, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********65701, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********65754, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********65757, "dur": 111, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********65877, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********65880, "dur": 107, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********65991, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********65993, "dur": 156, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********66155, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********66158, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********66202, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********66208, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********66246, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********66249, "dur": 283, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********66536, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********66540, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********66575, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********66578, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********66614, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********66616, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********66655, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********66688, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********66690, "dur": 98, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********66791, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********66794, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********66829, "dur": 1030, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********67864, "dur": 58, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********67926, "dur": 3, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********67931, "dur": 25, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********67958, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********67960, "dur": 204, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********68168, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********68171, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********68205, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********68208, "dur": 561, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********68775, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********68778, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********68829, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********68832, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********68878, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********68881, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********68938, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********68941, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********68988, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********68992, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********69044, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********69050, "dur": 73, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********69129, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********69132, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********69185, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********69190, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********69240, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********69244, "dur": 251, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********69499, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********69501, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********69546, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********69549, "dur": 1096, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********70651, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********70655, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********70710, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": ***********70718, "dur": 60989, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926331716, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926331724, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926331780, "dur": 2254, "ph": "X", "name": "ProcessMessages 205", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926334038, "dur": 15265, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926349314, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926349319, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926349372, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926349376, "dur": 76, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926349456, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926349459, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926349504, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926349509, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926349545, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926349549, "dur": 29, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926349582, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926349585, "dur": 516, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926350104, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926350107, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926350143, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926350148, "dur": 2566, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926352721, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926352726, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926352760, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926352764, "dur": 331, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926353100, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926353105, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926353150, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926353154, "dur": 132, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926353291, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926353294, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926353333, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926353336, "dur": 130, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926353471, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926353476, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926353516, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926353519, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926353575, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926353578, "dur": 85, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926353667, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926353671, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926353723, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926353727, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926353760, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926353763, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926353814, "dur": 107, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926353925, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926353930, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926353964, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926353966, "dur": 108, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926354079, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926354109, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926354111, "dur": 105, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926354220, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926354254, "dur": 107, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926354363, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926354365, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926354395, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926354397, "dur": 104, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926354505, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926354510, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926354548, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926354554, "dur": 231, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926354790, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926354793, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926354831, "dur": 11, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926354843, "dur": 121, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926354969, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926354973, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926355024, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926355028, "dur": 73, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926355105, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926355109, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926355147, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926355150, "dur": 104, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926355258, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926355260, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926355307, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926355310, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926355378, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926355380, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926355427, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926355430, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926355493, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926355495, "dur": 81, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926355581, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926355586, "dur": 1006, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926356600, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926356605, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926356656, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926356661, "dur": 118, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926356784, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926356787, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926356829, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926356833, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926356875, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926356878, "dur": 122, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926357005, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926357009, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926357055, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926357058, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926357105, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926357110, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926357158, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926357162, "dur": 106, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926357272, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926357275, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926357325, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926357328, "dur": 233, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926357566, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926357569, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926357609, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926357613, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926357651, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926357657, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926357701, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926357704, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926357751, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926357754, "dur": 86, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926357844, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926357847, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926357888, "dur": 4, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926357895, "dur": 841, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926358742, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926358748, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926358801, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926358805, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926358855, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926358858, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926358918, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926358922, "dur": 560, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926359487, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926359491, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926359538, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926359542, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926359597, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926359603, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926359653, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926359656, "dur": 392694, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926752361, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926752367, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926752440, "dur": 32, "ph": "X", "name": "ProcessMessages 10173", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926752476, "dur": 20021, "ph": "X", "name": "ReadAsync 10173", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926772506, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926772514, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926772551, "dur": 6, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748926772559, "dur": 297957, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748927070528, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748927070535, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748927070596, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748927070602, "dur": 26952, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748927097566, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748927097571, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748927097640, "dur": 30, "ph": "X", "name": "ProcessMessages 15119", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748927097672, "dur": 75049, "ph": "X", "name": "ReadAsync 15119", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748927172731, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748927172736, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748927172819, "dur": 31, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748927172853, "dur": 47624, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748927220487, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748927220494, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748927220549, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748927220555, "dur": 3230, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748927223796, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748927223801, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748927223868, "dur": 30, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748927223900, "dur": 249262, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748927473173, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748927473181, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748927473238, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748927473244, "dur": 1840, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748927475094, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748927475102, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748927475153, "dur": 23, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748927475179, "dur": 293790, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748927768977, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748927768980, "dur": 108, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748927769093, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748927769097, "dur": 91958, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748927861064, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748927861072, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748927861135, "dur": 29, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748927861168, "dur": 40813, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748927901988, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748927901992, "dur": 105, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748927902102, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748927902106, "dur": 1478, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748927903594, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748927903607, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748927903672, "dur": 29, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748927903703, "dur": 555186, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748928458897, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748928458901, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748928458938, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748928458943, "dur": 1745, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748928460698, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748928460703, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748928460768, "dur": 39, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748928460811, "dur": 627, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748928461443, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748928461447, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748928461504, "dur": 463, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 8368, "tid": 12884901888, "ts": 1749748928461971, "dur": 10538, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 8368, "tid": 706, "ts": 1749748928498319, "dur": 2402, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 8368, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 8368, "tid": 8589934592, "ts": ***********05284, "dur": 80906, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 8368, "tid": 8589934592, "ts": ***********86195, "dur": 9, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 8368, "tid": 8589934592, "ts": ***********86206, "dur": 2065, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 8368, "tid": 706, "ts": 1749748928500723, "dur": 8, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 8368, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 8368, "tid": 4294967296, "ts": 1749748926166656, "dur": 2308524, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 8368, "tid": 4294967296, "ts": 1749748926173457, "dur": 21829, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 8368, "tid": 4294967296, "ts": 1749748928475286, "dur": 12051, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 8368, "tid": 4294967296, "ts": 1749748928481228, "dur": 3956, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 8368, "tid": 4294967296, "ts": 1749748928487489, "dur": 27, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 8368, "tid": 706, "ts": 1749748928500734, "dur": 10, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": ***********17402, "dur": 162, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": ***********17613, "dur": 3262, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": ***********20896, "dur": 553, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": ***********21571, "dur": 538, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": ***********22183, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ScriptAssemblies"}}, {"pid": 12345, "tid": 0, "ts": ***********22256, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_74E5563AC8B9F787.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": ***********22324, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_A8A24F49E6ABD37C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": ***********22389, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_B2934FA2A35058C8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": ***********22497, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_327E491CC99787C2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": ***********22558, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_D06117315AA80255.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": ***********22833, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_82A472460B8D0C58.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": ***********22995, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_7C1ABDF389B33EB9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": ***********23192, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_ADD2331F72FC984C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": ***********23464, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_84455F0208ED5E29.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": ***********23627, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_023DD5DA5F2E2F04.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": ***********23843, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_40369253B7718F67.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": ***********24218, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_8CA73CEEF37C8951.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": ***********24542, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_AF225A1D88E5B371.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": ***********24809, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_BFB7C27956745739.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": ***********24917, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_34E341715FA456A1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": ***********25078, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_31CE8FDBC2A52507.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": ***********25754, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_167C723B68AFECBE.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": ***********26169, "dur": 436, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_B3174DE19DD84089.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": ***********26882, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_E9C32A05A5B46067.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": ***********29560, "dur": 1325, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_8A7F2F3530DD832B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": ***********30931, "dur": 112, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_4B16FAA5016376E3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": ***********31173, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_EE36537354EA42C8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": ***********31292, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": ***********31360, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 0, "ts": ***********31424, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp2"}}, {"pid": 12345, "tid": 0, "ts": ***********31691, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Barracuda.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": ***********31814, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Barracuda.rsp"}}, {"pid": 12345, "tid": 0, "ts": ***********31879, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Barracuda.rsp2"}}, {"pid": 12345, "tid": 0, "ts": ***********32057, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ML-Agents.CommunicatorObjects.ref.dll_D7E75170880C5095.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": ***********32114, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ML-Agents.CommunicatorObjects.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": ***********32177, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ML-Agents.CommunicatorObjects.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": ***********32821, "dur": 121, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ML-Agents.ref.dll_CCD3793145BE06C4.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": ***********32948, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ML-Agents.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": ***********33018, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ML-Agents.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": ***********33072, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ML-Agents.rsp"}}, {"pid": 12345, "tid": 0, "ts": ***********33262, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ML-Agents.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": ***********33488, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Barracuda.BurstBLAS.rsp"}}, {"pid": 12345, "tid": 0, "ts": ***********33556, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Barracuda.BurstBLAS.rsp2"}}, {"pid": 12345, "tid": 0, "ts": ***********33655, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Barracuda.BurstBLAS.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": ***********33788, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Barracuda.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": ***********33857, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Barracuda.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": ***********33918, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Barracuda.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": ***********33987, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Barracuda.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": ***********34115, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Barracuda.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": ***********34355, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Barracuda.MacBLAS.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": ***********34521, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Barracuda.MacBLAS.rsp2"}}, {"pid": 12345, "tid": 0, "ts": ***********34586, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Barracuda.MacBLAS.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": ***********34763, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Barracuda.iOSBLAS.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": ***********34820, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Barracuda.iOSBLAS.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": ***********34919, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Barracuda.iOSBLAS.rsp2"}}, {"pid": 12345, "tid": 0, "ts": ***********35221, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": ***********35285, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": ***********35381, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": ***********35527, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ML-Agents.Editor.ref.dll_A5EACA7A9EEE192D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": ***********35583, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ML-Agents.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": ***********35785, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ML-Agents.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": ***********35948, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.Editor.ref.dll_9A20CFD48BC11F43.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": ***********36004, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": ***********36099, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": ***********36199, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": ***********36259, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": ***********36389, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": ***********36455, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": ***********36520, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": ***********36576, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": ***********36642, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": ***********36958, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": ***********37127, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": ***********37194, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": ***********37334, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 0, "ts": ***********37672, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ML-Agents.CommunicatorObjects.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": ***********38078, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.dll"}}, {"pid": 12345, "tid": 0, "ts": ***********38202, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 0, "ts": ***********38723, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Barracuda.dll"}}, {"pid": 12345, "tid": 0, "ts": ***********38842, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7122174106763217432.rsp"}}, {"pid": 12345, "tid": 0, "ts": ***********39099, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.CodeGen.pdb"}}, {"pid": 12345, "tid": 0, "ts": ***********39170, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": ***********39308, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 0, "ts": ***********39599, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": ***********39666, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14814235663552238418.rsp"}}, {"pid": 12345, "tid": 0, "ts": ***********39916, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": ***********40037, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10831531723267835799.rsp"}}, {"pid": 12345, "tid": 0, "ts": ***********40265, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Barracuda.BurstBLAS.dll"}}, {"pid": 12345, "tid": 0, "ts": ***********40385, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7900087231598509626.rsp"}}, {"pid": 12345, "tid": 0, "ts": ***********40604, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Barracuda.MacBLAS.dll"}}, {"pid": 12345, "tid": 0, "ts": ***********40753, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15791729441920883851.rsp"}}, {"pid": 12345, "tid": 0, "ts": ***********40965, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Barracuda.ONNX.dll"}}, {"pid": 12345, "tid": 0, "ts": ***********41025, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Barracuda.ONNX.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": ***********41272, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Barracuda.ONNX.pdb"}}, {"pid": 12345, "tid": 0, "ts": ***********41330, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Barracuda.iOSBLAS.dll"}}, {"pid": 12345, "tid": 0, "ts": ***********41439, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12221609183782116845.rsp"}}, {"pid": 12345, "tid": 0, "ts": ***********41652, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ML-Agents.dll"}}, {"pid": 12345, "tid": 0, "ts": ***********41762, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1179049381252746301.rsp"}}, {"pid": 12345, "tid": 0, "ts": ***********42043, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Barracuda.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": ***********42365, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ML-Agents.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": ***********42717, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 0, "ts": ***********42790, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": ***********42858, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": ***********42919, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": ***********43076, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": ***********43144, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": ***********43573, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": ***********43651, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": ***********43707, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": ***********43762, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": ***********22166, "dur": 21989, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": ***********44164, "dur": 2216389, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749748928460554, "dur": 319, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749748928460874, "dur": 52, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749748928460932, "dur": 60, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749748928461116, "dur": 64, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749748928461251, "dur": 2533, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": ***********22618, "dur": 21557, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********44212, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********44453, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_96F78231E8E7F733.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********44533, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********44604, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_C3F223B142D71118.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********44689, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********44754, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_2D15011FD56E0F44.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********44877, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_E8776E32B252823F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********44958, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********45031, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_B474DF0B1FA4A14A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********45106, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********45178, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_5A6AE724F866AFF4.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********45248, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********45316, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_ABFDDE4BCE51AF97.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********45380, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********45443, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_EEDFD3A970FBC415.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********45508, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********45613, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_60DAFA72C55F6C39.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********45698, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********45760, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_28D6BAC7DB5B5809.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********45823, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********45886, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_F68B9FA2D6A0DB8B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********45949, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********46013, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_B7D1C0CF5B00C995.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********46077, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********46140, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_44BF067D63F8D033.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********46211, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********46278, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_A1F8F6BADD6B1D6F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********46418, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********46489, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_B730EF980809E0E5.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********46669, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********46746, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_634913B2E8485061.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********46833, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********46924, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_7261D772064E81CA.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********46993, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********47055, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_FB51922CB1BCF671.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********47121, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********47184, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_771C7F7AD95CE32D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********47249, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********47310, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_40369253B7718F67.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********47403, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********47512, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_64EB310D32B916F4.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********47618, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********47750, "dur": 404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_8C9D4507428A9FC0.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********48173, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********48457, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********48524, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********48791, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********48859, "dur": 10794, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": ***********59655, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********59829, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********59899, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********60257, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********60328, "dur": 2263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": ***********62592, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********62818, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********62908, "dur": 1255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********64163, "dur": 380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********64549, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********64695, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********64801, "dur": 524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": ***********65326, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********65463, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********65669, "dur": 731, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********66401, "dur": 2587, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********68990, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": ***********69225, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": ***********69301, "dur": 82112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749748926351415, "dur": 7727, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ML-Agents.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749748926359143, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749748926359286, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749748926359351, "dur": 2101186, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********22743, "dur": 21450, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********44212, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********44356, "dur": 597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_C31E81D076F68EFE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": ***********44953, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********45049, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_C750C2233B9790DD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": ***********45116, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********45183, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_993A99D98B91001A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": ***********45256, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********45347, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_66736A1D7FBBDA09.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": ***********45423, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********45489, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_F381EA8402AD6D80.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": ***********45560, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********45805, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_F381EA8402AD6D80.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": ***********45880, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_FDCBE8B5BECB59E6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": ***********46071, "dur": 639, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********46710, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_FDCBE8B5BECB59E6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": ***********46782, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_5D7F9D2DB2BC90A4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": ***********46983, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********47063, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_B2934FA2A35058C8.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": ***********47148, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********47244, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_09ED8B092E7B67EB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": ***********47316, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********47412, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_A6E9B6BE09D19AF3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": ***********47495, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********47563, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_83B62B76CC3B209D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": ***********47659, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********47732, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********47797, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 2, "ts": ***********47848, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********47932, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********48003, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********48110, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********48196, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********48265, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_4B16FAA5016376E3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": ***********48335, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********48450, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********48553, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********48619, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********48672, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": ***********48774, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********48837, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********48960, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********49052, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********49148, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********49218, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********49332, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********49429, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 2, "ts": ***********49494, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********49560, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********49615, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp2"}}, {"pid": 12345, "tid": 2, "ts": ***********49704, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********49804, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********49904, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********50090, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********50190, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********50258, "dur": 1148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********51407, "dur": 1233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********52640, "dur": 1477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********54117, "dur": 1885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********56003, "dur": 1814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********57818, "dur": 1232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********59051, "dur": 1148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********60200, "dur": 1159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********61359, "dur": 1267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********62629, "dur": 381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": ***********63011, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********63085, "dur": 920, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": ***********64006, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********64216, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********64318, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Editor.ref.dll_E2F92DAB6C167CC9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": ***********64489, "dur": 193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********64685, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********64772, "dur": 1266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********66043, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ML-Agents.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": ***********66280, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********66362, "dur": 1044, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ML-Agents.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": ***********67408, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********67614, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********67690, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ML-Agents.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": ***********67885, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********67970, "dur": 790, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ML-Agents.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": ***********68761, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********68981, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": ***********69222, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********69295, "dur": 490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": ***********70333, "dur": 91, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ***********71765, "dur": 485605, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749748926768514, "dur": 3032, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749748926767937, "dur": 3651, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749748926772119, "dur": 97, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749748927097290, "dur": 55, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749748926773537, "dur": 324786, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749748927109360, "dur": 654801, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749748927109320, "dur": 656775, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749748927768371, "dur": 306, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749748927769468, "dur": 91350, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749748927901314, "dur": 355, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1749748927901258, "dur": 412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1749748927903339, "dur": 51, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749748927901702, "dur": 1722, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1749748927903428, "dur": 557116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********22806, "dur": 21393, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********44204, "dur": 458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_244A58F537A25DB5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ***********44663, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********44733, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_40E4FF9BB9B5A8B2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ***********44810, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********44872, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_7E747FCC63A21C9C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ***********44967, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********45078, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_7E747FCC63A21C9C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ***********45171, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_D29FA68F597DE906.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ***********45258, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********45326, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_D0F306721B020377.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ***********45397, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********45461, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_436A901261937118.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ***********45535, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********45604, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_C7C3576D9F7C4B1D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ***********45665, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********45729, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_E2497FCB3878B6D2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ***********45796, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********45860, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_BFB7C27956745739.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ***********45922, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********45982, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_AF225A1D88E5B371.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ***********46050, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********46111, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_4BE0F815B18AE05D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ***********46179, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********46238, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_FE2933A1BDB5E02D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ***********46328, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********46391, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_84455F0208ED5E29.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ***********46461, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********46527, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_DFBC68533E86F38C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ***********46783, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********46851, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_DE9E651C604184CC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ***********46921, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********46976, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_DE9E651C604184CC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ***********47036, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_327E491CC99787C2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ***********47234, "dur": 335, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********47607, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_DBAFA68B528617AC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ***********47891, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********48206, "dur": 373, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********48580, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Barracuda.ONNX.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": ***********48727, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Barracuda.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": ***********49029, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********49159, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********49242, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********49353, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********49464, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********49529, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********49632, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********49754, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********49873, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********49973, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********50083, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********50184, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********50244, "dur": 1132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********51377, "dur": 1369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********52747, "dur": 1479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********54227, "dur": 1325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********55553, "dur": 1281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********56835, "dur": 1189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********59608, "dur": 889, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@8017b507cc74\\Unity.Mathematics.Editor\\MatrixDrawer.cs"}}, {"pid": 12345, "tid": 3, "ts": ***********58025, "dur": 3166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********61192, "dur": 1863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********63056, "dur": 1387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********64476, "dur": 74, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********64551, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********64688, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********64784, "dur": 1257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********66044, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Barracuda.BurstBLAS.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ***********66314, "dur": 1344, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********67666, "dur": 690, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Barracuda.BurstBLAS.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": ***********68358, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********68570, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ***********68638, "dur": 77478, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749748926346117, "dur": 3099, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Barracuda.BurstBLAS.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749748926349224, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749748926349353, "dur": 3501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749748926352856, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749748926353098, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749748926353280, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749748926353380, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749748926353530, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749748926353624, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749748926353800, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749748926353874, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749748926353948, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749748926354016, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ML-Agents.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749748926354093, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749748926354162, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ML-Agents.CommunicatorObjects.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749748926354234, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749748926354346, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749748926354576, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749748926354651, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749748926354795, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749748926354948, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749748926355065, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749748926355182, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749748926355304, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749748926355390, "dur": 997, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1749748926356397, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749748926356623, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749748926356909, "dur": 640, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749748926357550, "dur": 1739, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749748926359300, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749748926359388, "dur": 2101140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********22916, "dur": 21294, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********44214, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_AFD0C7658B29CCBC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": ***********44526, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********44612, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_AB9265FD87FD90C5.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": ***********44698, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********44767, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_A0768603EA532F07.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": ***********44849, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********44953, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_A0768603EA532F07.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": ***********45020, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_E9C32A05A5B46067.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": ***********45103, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********45205, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_A903C24A5E4013F4.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": ***********45309, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********45403, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_57B88D0B98461338.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": ***********45508, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********45594, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_762814B88828AE51.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": ***********45720, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********45853, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_6B6CB0EFF719FCC0.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": ***********45942, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********46031, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_ABF2811D4E30BF31.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": ***********46123, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********46193, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_D1776B06DDC829D3.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": ***********46263, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********46384, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_FD71587F5F257E23.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": ***********46468, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********46707, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_ADD2331F72FC984C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": ***********46820, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********46911, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_BC3B8CD112883730.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": ***********47009, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********47100, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_A9DB427F6297308B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": ***********47189, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********47280, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_D5C8FD9DF63A0488.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": ***********47396, "dur": 1038, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********48678, "dur": 1701, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": ***********50381, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********50444, "dur": 11607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": ***********62053, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********62283, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": ***********62537, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********62612, "dur": 842, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": ***********63455, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********63533, "dur": 966, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": ***********64501, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********64681, "dur": 714, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 4, "ts": ***********65396, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********65463, "dur": 113, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": ***********66709, "dur": 64824, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 4, "ts": 1749748926346105, "dur": 2791, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ML-Agents.CommunicatorObjects.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749748926348899, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749748926349088, "dur": 3240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Barracuda.MacBLAS.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749748926352330, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749748926352513, "dur": 3880, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749748926356394, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749748926356672, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749748926356825, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749748926356918, "dur": 454, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749748926357376, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749748926357450, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749748926357518, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749748926357609, "dur": 410335, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749748926768029, "dur": 294835, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749748926767946, "dur": 298138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749748927069691, "dur": 567, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749748927071621, "dur": 100984, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749748927219780, "dur": 253135, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749748927219723, "dur": 253194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749748927472943, "dur": 1949, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749748927474896, "dur": 985630, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********22997, "dur": 21225, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********44227, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_890EF29A580DFE7E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": ***********44555, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********44640, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_C30E91755D7FDA53.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": ***********44710, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********44774, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_AEE27832734BE38D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": ***********44847, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********44999, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_C102A7FD7A53C08D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": ***********45185, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********45329, "dur": 252, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_C102A7FD7A53C08D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": ***********45583, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_DA70E4B1900EDB02.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": ***********45664, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********45753, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_2D6B42F0D2052247.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": ***********45951, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********46119, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_8CA73CEEF37C8951.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": ***********46193, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********46259, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_411DA61D81DDAB92.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": ***********46410, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********46684, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_BB3CD86E6819B008.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": ***********46758, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********46830, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_82A472460B8D0C58.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": ***********46894, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********46956, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_376502286C604E7C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": ***********47019, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********47082, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_A8A24F49E6ABD37C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": ***********47158, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********47258, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_88EE0A517078EB38.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": ***********47415, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********47624, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_8105CE7FC817C19B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": ***********47725, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********47834, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********47908, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Barracuda.rsp"}}, {"pid": 12345, "tid": 5, "ts": ***********47987, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********48082, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_8A7F2F3530DD832B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": ***********48194, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********48275, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_6717AAFEBC09DAE0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": ***********48342, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********48426, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********48544, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********48614, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********48672, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Barracuda.iOSBLAS.rsp2"}}, {"pid": 12345, "tid": 5, "ts": ***********48783, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********48996, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********49061, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********49150, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********49230, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********49291, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Barracuda.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": ***********49343, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********49450, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********49545, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********49674, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********49801, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********49906, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********50006, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********50168, "dur": 1204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********51372, "dur": 2022, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********53394, "dur": 1753, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********55147, "dur": 1561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********56709, "dur": 1886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********58595, "dur": 1635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********60230, "dur": 1823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********62054, "dur": 1189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********63970, "dur": 549, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.barracuda@82bcfb51cf28\\Barracuda\\Runtime\\Core\\Backends\\BarracudaCompute.cs"}}, {"pid": 12345, "tid": 5, "ts": ***********63243, "dur": 1707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********64951, "dur": 1093, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********66047, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Barracuda.MacBLAS.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": ***********66356, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********66463, "dur": 883, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Barracuda.MacBLAS.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": ***********67347, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********67536, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********67599, "dur": 20516, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********91558, "dur": 770, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 5, "ts": ***********92330, "dur": 2825, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 5, "ts": ***********95161, "dur": 272, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 5, "ts": ***********88116, "dur": 7322, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": ***********95439, "dur": 50668, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749748926346112, "dur": 2993, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749748926349107, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749748926349272, "dur": 7048, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Barracuda.iOSBLAS.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749748926356321, "dur": 436, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749748926356758, "dur": 166, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Barracuda.iOSBLAS.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749748926356934, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749748926357079, "dur": 390, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749748926357480, "dur": 1055, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749748926358539, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749748926358688, "dur": 2101855, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********23077, "dur": 21165, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********44246, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_62EE8B325A93F221.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": ***********44596, "dur": 280, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********44885, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_967CD95ECFA4FCA6.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": ***********44979, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********45079, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_6E5F1C40D32AC565.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": ***********45258, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********45450, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_112B10D1F64F1B9D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": ***********45729, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********45813, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_34E341715FA456A1.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": ***********45880, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********45939, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_2E02C0810F99500C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": ***********46013, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********46074, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_7073D287F5850ADC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": ***********46143, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********46207, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_54F5E93C64F45A9A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": ***********46279, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********46345, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_023DD5DA5F2E2F04.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": ***********46414, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********46476, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_1EAF4C94EAA0A866.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": ***********46669, "dur": 753, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********47431, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_E9C576736994A562.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": ***********47513, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********47608, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_74E5563AC8B9F787.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": ***********47701, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********47818, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 6, "ts": ***********47926, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********48029, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********48153, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********48214, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********48342, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********48432, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********48537, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Barracuda.ONNX.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": ***********48602, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 6, "ts": ***********48705, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********48782, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********48848, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********48909, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********48986, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********49054, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********49134, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********49213, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********49277, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ML-Agents.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": ***********49332, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********49413, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": ***********49497, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********49575, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********49644, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********49759, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********49857, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********49954, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********50065, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********50135, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10831531723267835799.rsp"}}, {"pid": 12345, "tid": 6, "ts": ***********50198, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********50266, "dur": 1208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********51474, "dur": 1726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********53201, "dur": 1684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********54885, "dur": 1304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********56189, "dur": 1176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********57366, "dur": 1132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********58498, "dur": 1108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********59607, "dur": 1230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********60838, "dur": 824, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********61662, "dur": 1311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********62973, "dur": 1571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********64549, "dur": 139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********64689, "dur": 3048, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": ***********67738, "dur": 78375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749748926346115, "dur": 2986, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ML-Agents.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749748926349103, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749748926349258, "dur": 3493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749748926352753, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749748926352898, "dur": 5472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749748926358373, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749748926358532, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749748926358609, "dur": 2101920, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********23158, "dur": 21103, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********44266, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_D821EA3D2A836382.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": ***********44575, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********44654, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_E6637D2C122CDC45.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": ***********44737, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********44809, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_84F3CC4A0B317774.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": ***********44947, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********45047, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_94FACAAD5EDFB72B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": ***********45198, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********45271, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_B3174DE19DD84089.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": ***********45355, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********45433, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_167C723B68AFECBE.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": ***********45512, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********45587, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_3D11C499DC1B5C00.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": ***********45667, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********45736, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_31CE8FDBC2A52507.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": ***********45826, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********45909, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_1582EA272108E707.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": ***********45988, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********46056, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_2E0D0C3FE26D9794.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": ***********46139, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********46201, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_0D28A08C7800286E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": ***********46266, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********46363, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_16144E5172844416.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": ***********46451, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********46520, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_C88E8EE37E226AB7.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": ***********46739, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********46845, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_ECDED007137C373E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": ***********46917, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********46979, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_D06117315AA80255.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": ***********47049, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********47113, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_69D0C5AC93E3EBE9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": ***********47173, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********47235, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_85E47EDD87F187DE.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": ***********47301, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********47372, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_BAA2C9F46934AE73.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": ***********47473, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********47541, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_B61C4A287BCC4EA2.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": ***********47615, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********47686, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********47774, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********47841, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********47926, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********48000, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********48077, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_44EFE7EE711951EC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": ***********48143, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********48206, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********48290, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_7E6A236E3ABEDC63.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": ***********48405, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_7E6A236E3ABEDC63.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": ***********48488, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ML-Agents.CommunicatorObjects.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": ***********48725, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********48798, "dur": 11766, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ML-Agents.CommunicatorObjects.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": ***********60565, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********60744, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********60825, "dur": 1061, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********61887, "dur": 1059, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********62946, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********63138, "dur": 1156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********64478, "dur": 225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********64703, "dur": 1356, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********66061, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Barracuda.iOSBLAS.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": ***********66297, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********66381, "dur": 977, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Barracuda.iOSBLAS.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": ***********67361, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********67577, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********67649, "dur": 27795, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": ***********95445, "dur": 50670, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749748926346116, "dur": 2988, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Barracuda.ONNX.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749748926349105, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749748926349341, "dur": 5658, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749748926355001, "dur": 567, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749748926355568, "dur": 1797, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749748926357374, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749748926357620, "dur": 862137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749748927219825, "dur": 395, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1749748927219759, "dur": 463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1749748927220316, "dur": 3272, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1749748927223593, "dur": 1236938, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********23235, "dur": 21033, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********44269, "dur": 337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_0B06B842067A481C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": ***********44607, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********44697, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_5ABBB8CB772E6B1F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": ***********44774, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********44837, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_A9ABA8426D18C82A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": ***********44951, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********45143, "dur": 237, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_A9ABA8426D18C82A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": ***********45382, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_C11303D5770A4023.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": ***********45573, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********45664, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_415A8B70C18C3315.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": ***********45748, "dur": 955, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********46726, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_7C1ABDF389B33EB9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": ***********46994, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********47179, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_7C1ABDF389B33EB9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": ***********47230, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_45501ECF81D0581E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": ***********47297, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********47382, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_6073C21138BD4CD7.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": ***********47466, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********47533, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_E3EB23B1D128DC36.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": ***********47603, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********47665, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_9B2DBE84DC37162B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": ***********47732, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********47831, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********47919, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********48064, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Barracuda.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": ***********48121, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_EC1F0C20321316E3.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": ***********48192, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********48256, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_CDF7FB74A9A34663.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": ***********48325, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********48404, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********48486, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": ***********48704, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********48763, "dur": 15507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": ***********64271, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********64478, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********64548, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Barracuda.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": ***********64748, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********64818, "dur": 944, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Barracuda.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": ***********65764, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********65959, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********66037, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Barracuda.ONNX.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": ***********66266, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********66347, "dur": 689, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Barracuda.ONNX.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": ***********67037, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********67257, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********67335, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Barracuda.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": ***********67650, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********67728, "dur": 774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Barracuda.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": ***********68503, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********68740, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": ***********68825, "dur": 77285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749748926346116, "dur": 3647, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Barracuda.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749748926349765, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749748926349910, "dur": 7427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Barracuda.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749748926357339, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749748926357519, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749748926357633, "dur": 1543684, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749748927901385, "dur": 557222, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749748927901318, "dur": 557291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749748928458640, "dur": 1843, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1749748928467765, "dur": 3847, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 8368, "tid": 706, "ts": 1749748928501718, "dur": 4630, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 8368, "tid": 706, "ts": 1749748928506469, "dur": 3504, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 8368, "tid": 706, "ts": 1749748928495017, "dur": 16127, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}