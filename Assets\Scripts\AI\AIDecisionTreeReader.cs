using UnityEngine;
using System.Collections.Generic;
using System.IO;

/// <summary>
/// Runtime AI Decision Tree Reader
/// Loads and interprets ai_decision_tree.json for dynamic agent behavior
/// </summary>
[System.Serializable]
public class AIDecisionTreeReader : MonoBehaviour
{
    [Header("🧠 Decision Tree Configuration")]
    public TextAsset decisionTreeJson;
    public bool enableDynamicLoading = true;
    public float updateInterval = 1f;
    
    [Header("📊 Runtime Status")]
    public bool isLoaded = false;
    public string currentProfile = "";
    public int totalStates = 0;
    public int totalActions = 0;
    
    // Decision tree data structures
    private AIDecisionTree decisionTree;
    private Dictionary<string, AgentRole> roleDatabase = new Dictionary<string, AgentRole>();
    private Dictionary<string, StateAction> stateActions = new Dictionary<string, StateAction>();
    
    // Events
    public System.Action<AIDecisionTree> OnDecisionTreeLoaded;
    public System.Action<string> OnDecisionTreeError;
    
    void Start()
    {
        LoadDecisionTree();
        
        if (enableDynamicLoading)
        {
            InvokeRepeating(nameof(CheckForUpdates), updateInterval, updateInterval);
        }
    }
    
    public void LoadDecisionTree()
    {
        try
        {
            string jsonContent = "";
            
            // Try to load from TextAsset first
            if (decisionTreeJson != null)
            {
                jsonContent = decisionTreeJson.text;
                Debug.Log("📄 Loading decision tree from TextAsset");
            }
            // Fallback to file system
            else if (File.Exists("ai_decision_tree.json"))
            {
                jsonContent = File.ReadAllText("ai_decision_tree.json");
                Debug.Log("📄 Loading decision tree from file system");
            }
            else
            {
                Debug.LogWarning("⚠️ No decision tree found, creating default");
                CreateDefaultDecisionTree();
                return;
            }
            
            // Parse JSON
            decisionTree = JsonUtility.FromJson<AIDecisionTree>(jsonContent);
            
            if (decisionTree != null)
            {
                ProcessDecisionTree();
                isLoaded = true;
                OnDecisionTreeLoaded?.Invoke(decisionTree);
                Debug.Log($"✅ Decision tree loaded: {decisionTree.agent_profile.name}");
            }
            else
            {
                throw new System.Exception("Failed to parse JSON");
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"❌ Failed to load decision tree: {e.Message}");
            OnDecisionTreeError?.Invoke(e.Message);
            CreateDefaultDecisionTree();
        }
    }
    
    void ProcessDecisionTree()
    {
        // Process agent profile
        currentProfile = decisionTree.agent_profile.name;
        
        // Process roles
        roleDatabase.Clear();
        if (decisionTree.roles != null)
        {
            foreach (var roleKvp in decisionTree.roles)
            {
                roleDatabase[roleKvp.Key] = roleKvp.Value;
            }
        }
        
        // Process states and actions
        stateActions.Clear();
        if (decisionTree.states != null)
        {
            foreach (var stateKvp in decisionTree.states)
            {
                foreach (var conditionKvp in stateKvp.Value)
                {
                    string key = $"{stateKvp.Key}_{conditionKvp.Key}";
                    stateActions[key] = new StateAction
                    {
                        state = stateKvp.Key,
                        condition = conditionKvp.Key,
                        action = conditionKvp.Value
                    };
                }
            }
        }
        
        totalStates = decisionTree.states?.Count ?? 0;
        totalActions = decisionTree.actions?.Count ?? 0;
        
        Debug.Log($"📊 Processed {totalStates} states, {totalActions} actions, {roleDatabase.Count} roles");
    }
    
    public string GetAction(string state, string condition)
    {
        string key = $"{state}_{condition}";
        if (stateActions.ContainsKey(key))
        {
            return stateActions[key].action;
        }
        
        // Fallback to default action
        return GetDefaultAction(state);
    }
    
    public AgentRole GetRole(string roleName)
    {
        if (roleDatabase.ContainsKey(roleName))
        {
            return roleDatabase[roleName];
        }
        
        Debug.LogWarning($"⚠️ Role '{roleName}' not found, returning default");
        return GetDefaultRole();
    }
    
    public float GetReward(string rewardType)
    {
        if (decisionTree?.rewards != null && decisionTree.rewards.ContainsKey(rewardType))
        {
            return decisionTree.rewards[rewardType];
        }
        
        return 0f;
    }
    
    public List<string> GetPreferredWeapons(string roleName)
    {
        AgentRole role = GetRole(roleName);
        return role?.preferred_weapons ?? new List<string>();
    }
    
    public List<string> GetRolePriorities(string roleName)
    {
        AgentRole role = GetRole(roleName);
        return role?.priorities ?? new List<string>();
    }
    
    public bool ShouldAvoidWeapon(string roleName, string weaponName)
    {
        AgentRole role = GetRole(roleName);
        return role?.avoid_weapons?.Contains(weaponName) ?? false;
    }
    
    string GetDefaultAction(string state)
    {
        switch (state)
        {
            case "player_down": return "move_to_teammate_and_revive";
            case "low_health": return "use_medkit";
            case "enemy_detected": return "seek_cover_and_call_backup";
            case "zone_control": return "patrol_nearest_zone";
            case "idle": return "patrol_nearest_zone";
            default: return "patrol_nearest_zone";
        }
    }
    
    AgentRole GetDefaultRole()
    {
        return new AgentRole
        {
            priorities = new List<string> { "StayBehind", "PingEnemy", "SupplyDrop" },
            preferred_weapons = new List<string> { "M416", "UMP45", "FirstAidKit" },
            avoid_weapons = new List<string> { "SniperRifle" }
        };
    }
    
    void CreateDefaultDecisionTree()
    {
        Debug.Log("🔧 Creating default decision tree");
        
        decisionTree = new AIDecisionTree
        {
            agent_profile = new AgentProfile
            {
                name = "VictorBot",
                role = "Support",
                preferred_weapons = new List<string> { "M416", "UMP45", "FirstAidKit" },
                tactical_focus = new List<string> { "Revive", "StickToSquad", "CoverFire" },
                playstyle = "Balanced"
            },
            states = new Dictionary<string, Dictionary<string, string>>
            {
                ["player_down"] = new Dictionary<string, string>
                {
                    ["enemy_nearby"] = "deploy_smoke_then_revive",
                    ["no_enemy"] = "move_to_teammate_and_revive"
                },
                ["low_health"] = new Dictionary<string, string>
                {
                    ["has_medkit"] = "use_medkit",
                    ["no_medkit"] = "fallback_and_loot"
                },
                ["enemy_detected"] = new Dictionary<string, string>
                {
                    ["alone"] = "seek_cover_and_call_backup",
                    ["in_squad"] = "engage_or_flank_based_on_position"
                }
            },
            rewards = new Dictionary<string, float>
            {
                ["revive_success"] = 0.4f,
                ["loot_high_tier"] = 0.3f,
                ["flank_success"] = 0.5f,
                ["squad_following"] = 0.05f
            }
        };
        
        ProcessDecisionTree();
        isLoaded = true;
    }
    
    void CheckForUpdates()
    {
        if (!enableDynamicLoading) return;
        
        // Check if file has been modified (simplified check)
        if (File.Exists("ai_decision_tree.json"))
        {
            var lastWrite = File.GetLastWriteTime("ai_decision_tree.json");
            // Reload if file is newer than 1 second (simple hot-reload)
            if ((System.DateTime.Now - lastWrite).TotalSeconds < 2)
            {
                Debug.Log("🔄 Decision tree file updated, reloading...");
                LoadDecisionTree();
            }
        }
    }
    
    public void SaveDecisionTree()
    {
        if (decisionTree != null)
        {
            string json = JsonUtility.ToJson(decisionTree, true);
            File.WriteAllText("ai_decision_tree_runtime.json", json);
            Debug.Log("💾 Decision tree saved to runtime file");
        }
    }
    
    public AIDecisionTree GetDecisionTree()
    {
        return decisionTree;
    }
    
    public Dictionary<string, AgentRole> GetAllRoles()
    {
        return new Dictionary<string, AgentRole>(roleDatabase);
    }
    
    void OnValidate()
    {
        if (Application.isPlaying && isLoaded)
        {
            LoadDecisionTree();
        }
    }
}

// Data structures for decision tree
[System.Serializable]
public class AIDecisionTree
{
    public AgentProfile agent_profile;
    public Dictionary<string, Dictionary<string, string>> states;
    public Dictionary<string, ActionDefinition> actions;
    public Dictionary<string, AgentRole> roles;
    public Dictionary<string, float> rewards;
}

[System.Serializable]
public class AgentProfile
{
    public string name;
    public string role;
    public List<string> preferred_weapons;
    public List<string> tactical_focus;
    public string playstyle;
}

[System.Serializable]
public class ActionDefinition
{
    public List<string> conditions;
    public string result;
}

[System.Serializable]
public class AgentRole
{
    public List<string> priorities;
    public List<string> preferred_weapons;
    public List<string> avoid_weapons;
}

[System.Serializable]
public class StateAction
{
    public string state;
    public string condition;
    public string action;
}
