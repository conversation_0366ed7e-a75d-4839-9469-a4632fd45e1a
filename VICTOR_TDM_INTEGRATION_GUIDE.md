# 🎮 Victor 5v5 TDM Integration Guide

## 🚀 Quick Start - One-Click Setup

**Use the automated setup tool:**
```
Unity Editor → SquadMate AI → 🎮 Victor TDM Setup → 🎮 Create Complete TDM Scene
```

This will automatically set up the entire 5v5 TDM system with Victor agents!

## 📁 Asset Integration Steps

### 1. **Import Assets**
```
1. Copy victor/ folder to Assets/Models/Victor/
2. Copy tdm/ folder to Assets/Models/TDM/
3. Refresh Unity (Ctrl+R)
```

### 2. **Auto-Setup (Recommended)**
```
1. Open: SquadMate AI → 🎮 Victor TDM Setup
2. Click: 🔍 Auto-Find Assets
3. Click: 🎮 Create Complete TDM Scene
4. Done! ✅
```

### 3. **Manual Setup (Advanced)**
```
1. Create Victor materials from textures
2. Setup Victor agent prefab with components
3. Import TDM arena and mark static
4. Create spawn points for both teams
5. Setup capture zones and weapon spawns
6. Configure SquadManager
```

## 🧍‍♂️ Victor Agent Features

### **Enhanced AI Components**
- ✅ **VictorAgent** - Extends SquadMateAgent for TDM
- ✅ **Team Coordination** - 5v5 squad tactics
- ✅ **Zone Control** - Capture and hold objectives
- ✅ **Combat Engagement** - Advanced TDM combat
- ✅ **Visual Team Identification** - Blue vs Red teams

### **PUBG-Style Systems**
- ✅ **PUBGInventory** - Advanced loot management
- ✅ **PUBGArmorSystem** - Damage reduction system
- ✅ **WeaponSystem** - Realistic weapon handling
- ✅ **HealthSystem** - Tactical healing mechanics
- ✅ **DynamicRewardSystem** - Smart reward calculation

## 🗺️ TDM Arena Features

### **5v5 Team Deathmatch**
- ✅ **Team-based spawning** - Separate spawn zones
- ✅ **Capture zones** - 5 strategic control points
- ✅ **Weapon spawns** - 8 weapon pickup locations
- ✅ **Health spawns** - Medical supply points
- ✅ **Armor spawns** - Tactical gear locations

### **Match Management**
- ✅ **Score tracking** - Kill-based scoring system
- ✅ **Time limits** - 10-minute matches
- ✅ **Respawn system** - 3-second respawn delay
- ✅ **Team balancing** - Automatic 5v5 balance

## 🧠 Training Configuration

### **Optimized for 5v5 TDM**
```yaml
behaviors:
  VictorTDM:
    trainer_type: poca  # Multi-agent training
    hyperparameters:
      batch_size: 2048
      buffer_size: 20480
      learning_rate: 3e-4
    
    self_play:
      save_steps: 50000
      team_change: 200000
      play_against_latest_model_ratio: 0.5
    
    max_steps: 5000000  # Extended for team tactics
    time_horizon: 1000  # Longer episodes for strategy
```

### **Advanced Features**
- ✅ **Self-Play Training** - Teams learn against each other
- ✅ **Curriculum Learning** - Progressive difficulty
- ✅ **Multi-Agent Coordination** - Team-based rewards
- ✅ **Competitive Training** - Adversarial learning

## 🎯 Training Objectives

### **Individual Agent Goals**
- 🎯 **Eliminate enemies** (**** reward)
- 🛡️ **Survive encounters** (+0.1 reward)
- 🎒 **Collect tactical loot** (+0.3 reward)
- 💊 **Heal strategically** (+0.2 reward)

### **Team Coordination Goals**
- 🤝 **Stay with squad** (+0.005 reward/frame)
- 🎯 **Control capture zones** (+0.01 reward/frame)
- 📡 **Share enemy intel** (+0.02 reward)
- ♻️ **Revive teammates** (+0.5 reward)

### **Tactical Objectives**
- 🏃‍♂️ **Flanking maneuvers** (+0.15 reward)
- 🛡️ **Cover usage** (+0.05 reward)
- 🎯 **Zone control** (+0.02 reward/frame)
- ⚔️ **Combat engagement** (+0.005 reward)

## 📊 Expected Training Results

### **Phase 1: Basic Movement (0-500k steps)**
- ✅ Team spawning and basic movement
- ✅ Simple weapon pickup
- ✅ Basic enemy engagement

### **Phase 2: Team Coordination (500k-2M steps)**
- ✅ Squad formation maintenance
- ✅ Coordinated attacks
- ✅ Zone capture basics

### **Phase 3: Advanced Tactics (2M-5M steps)**
- ✅ Flanking maneuvers
- ✅ Strategic zone control
- ✅ Advanced team coordination
- ✅ Tactical healing and cover usage

## 🎮 Controls & Testing

### **Manual Testing Controls**
```
WASD - Movement
Mouse - Look around
Space - Attack/Shoot
E - Pickup items
H - Heal
R - Revive teammate
T - Move to capture zone
Y - Follow squad leader
U - Flank enemy
```

### **Training Monitoring**
```bash
# Start training
python train_squadmate.py --config=config/victor_tdm_config.yaml --run-id=victor_tdm

# Monitor progress
tensorboard --logdir results

# Key metrics to watch:
- Team A vs Team B win rate
- Average episode length
- Zone control percentage
- Kill/Death ratios
```

## 🔧 Troubleshooting

### **Common Issues**

**Victor model not appearing:**
```
1. Check if Victor.fbx is in Assets/Models/Victor/
2. Verify materials are assigned correctly
3. Run: Victor TDM Setup → Setup Victor Prefab
```

**TDM arena missing:**
```
1. Check if Tdm.fbx is in Assets/Models/TDM/
2. Verify arena is marked as static
3. Run: Victor TDM Setup → Setup TDM Arena
```

**Agents not spawning:**
```
1. Check spawn points are created
2. Verify SquadManager has Victor prefab assigned
3. Check team size settings (default: 5v5)
```

**Training not starting:**
```
1. Verify ML-Agents installation
2. Check config file syntax
3. Ensure scene has proper setup
4. Run: Victor TDM Setup → Validate TDM Setup
```

### **Performance Optimization**
```
1. Reduce visual quality for training
2. Use time_scale: 20 for faster training
3. Enable no_graphics mode for headless training
4. Optimize NavMesh complexity
```

## 🎉 Success Indicators

**Your Victor TDM system is working when:**
- ✅ 10 Victor agents spawn (5 blue, 5 red)
- ✅ Teams engage in tactical combat
- ✅ Agents capture and hold zones
- ✅ Coordinated team movements emerge
- ✅ Strategic healing and looting behaviors
- ✅ Competitive match dynamics develop

## 🚀 Next Steps

1. **Start Training**: Use the generated config for 5v5 training
2. **Monitor Progress**: Watch team coordination develop
3. **Tune Rewards**: Adjust for desired behaviors
4. **Export Models**: Save trained .onnx files
5. **Deploy**: Use trained agents in your game

---

**🎮 Your Victor 5v5 TDM system is now ready for advanced tactical AI training!**

The system combines PUBG-style mechanics with team-based objectives, creating sophisticated AI agents that can work together in competitive scenarios.
