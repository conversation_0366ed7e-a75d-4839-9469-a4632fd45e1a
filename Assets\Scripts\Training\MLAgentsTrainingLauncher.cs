using UnityEngine;
using System.Diagnostics;
using System.IO;
using UnityEditor;

/// <summary>
/// One-click ML-Agents training launcher for SquadMate AI
/// Handles training setup, configuration validation, and process management
/// </summary>
public class MLAgentsTrainingLauncher : MonoBehaviour
{
    [Header("🚀 Training Configuration")]
    public string configFileName = "squadmate-ppo.yaml";
    public string runId = "squadmate-arena-v1";
    public bool resumeTraining = false;
    public bool useGPU = true;
    public int numEnvs = 1;

    [Header("📁 Paths")]
    public string configPath = "config";
    public string resultsPath = "results";
    public string modelsPath = "models";

    [Header("🎯 Training Parameters")]
    public int maxSteps = 5000000;
    public bool autoExportONNX = true;
    public bool enableTensorBoard = true;
    public int tensorBoardPort = 6006;

    [Header("🔧 Advanced Settings")]
    public bool verboseLogging = true;
    public bool debugMode = false;
    public string additionalArgs = "";

    private Process trainingProcess;
    private bool isTraining = false;

    void Start()
    {
        ValidateSetup();
    }

    [ContextMenu("Start Training")]
    public void StartTraining()
    {
        if (isTraining)
        {
            UnityEngine.Debug.LogWarning("⚠️ Training is already running!");
            return;
        }

        if (!ValidateSetup())
        {
            UnityEngine.Debug.LogError("❌ Training setup validation failed!");
            return;
        }

        UnityEngine.Debug.Log("🚀 Starting ML-Agents training...");
        
        string command = BuildTrainingCommand();
        UnityEngine.Debug.Log($"📝 Training command: {command}");
        
        StartTrainingProcess(command);
    }

    [ContextMenu("Stop Training")]
    public void StopTraining()
    {
        if (trainingProcess != null && !trainingProcess.HasExited)
        {
            trainingProcess.Kill();
            trainingProcess = null;
            isTraining = false;
            UnityEngine.Debug.Log("🛑 Training stopped");
        }
        else
        {
            UnityEngine.Debug.LogWarning("⚠️ No training process to stop");
        }
    }

    [ContextMenu("Open TensorBoard")]
    public void OpenTensorBoard()
    {
        if (!enableTensorBoard)
        {
            UnityEngine.Debug.LogWarning("⚠️ TensorBoard is disabled");
            return;
        }

        string tensorBoardCommand = $"tensorboard --logdir={resultsPath} --port={tensorBoardPort}";
        
        ProcessStartInfo startInfo = new ProcessStartInfo()
        {
            FileName = "cmd.exe",
            Arguments = $"/c {tensorBoardCommand}",
            UseShellExecute = false,
            CreateNoWindow = false
        };

        Process.Start(startInfo);
        UnityEngine.Debug.Log($"📊 TensorBoard started on http://localhost:{tensorBoardPort}");
    }

    [ContextMenu("Export ONNX Model")]
    public void ExportONNXModel()
    {
        if (!Directory.Exists(resultsPath))
        {
            UnityEngine.Debug.LogError($"❌ Results directory not found: {resultsPath}");
            return;
        }

        string exportCommand = $"mlagents-learn --resume --run-id={runId} --export-onnx";
        
        ProcessStartInfo startInfo = new ProcessStartInfo()
        {
            FileName = "cmd.exe",
            Arguments = $"/c {exportCommand}",
            UseShellExecute = false,
            CreateNoWindow = false,
            WorkingDirectory = Application.dataPath + "/.."
        };

        Process exportProcess = Process.Start(startInfo);
        UnityEngine.Debug.Log("📦 Exporting ONNX model...");
    }

    bool ValidateSetup()
    {
        bool isValid = true;

        // Check if config file exists
        string fullConfigPath = Path.Combine(Application.dataPath, "..", configPath, configFileName);
        if (!File.Exists(fullConfigPath))
        {
            UnityEngine.Debug.LogError($"❌ Config file not found: {fullConfigPath}");
            isValid = false;
        }

        // Check if ML-Agents is installed
        if (!IsMLAgentsInstalled())
        {
            UnityEngine.Debug.LogError("❌ ML-Agents not found. Please install with: pip install mlagents");
            isValid = false;
        }

        // Create directories if they don't exist
        CreateDirectoryIfNotExists(resultsPath);
        CreateDirectoryIfNotExists(modelsPath);

        // Check for SquadMateAgent in scene
        if (FindObjectOfType<SquadMateAgent>() == null)
        {
            UnityEngine.Debug.LogWarning("⚠️ No SquadMateAgent found in scene. Make sure to add agents before training.");
        }

        return isValid;
    }

    bool IsMLAgentsInstalled()
    {
        try
        {
            ProcessStartInfo startInfo = new ProcessStartInfo()
            {
                FileName = "mlagents-learn",
                Arguments = "--help",
                UseShellExecute = false,
                CreateNoWindow = true,
                RedirectStandardOutput = true,
                RedirectStandardError = true
            };

            using (Process process = Process.Start(startInfo))
            {
                process.WaitForExit(5000); // Wait max 5 seconds
                return process.ExitCode == 0;
            }
        }
        catch
        {
            return false;
        }
    }

    void CreateDirectoryIfNotExists(string path)
    {
        string fullPath = Path.Combine(Application.dataPath, "..", path);
        if (!Directory.Exists(fullPath))
        {
            Directory.CreateDirectory(fullPath);
            UnityEngine.Debug.Log($"📁 Created directory: {fullPath}");
        }
    }

    string BuildTrainingCommand()
    {
        string command = "mlagents-learn";
        
        // Config file
        command += $" {configPath}/{configFileName}";
        
        // Run ID
        command += $" --run-id={runId}";
        
        // Resume training
        if (resumeTraining)
        {
            command += " --resume";
        }
        
        // Force overwrite
        if (!resumeTraining)
        {
            command += " --force";
        }
        
        // Number of environments
        if (numEnvs > 1)
        {
            command += $" --num-envs={numEnvs}";
        }
        
        // GPU usage
        if (useGPU)
        {
            command += " --torch-device=cuda";
        }
        
        // Export ONNX
        if (autoExportONNX)
        {
            command += " --export-onnx";
        }
        
        // Debug mode
        if (debugMode)
        {
            command += " --debug";
        }
        
        // Additional arguments
        if (!string.IsNullOrEmpty(additionalArgs))
        {
            command += $" {additionalArgs}";
        }

        return command;
    }

    void StartTrainingProcess(string command)
    {
        ProcessStartInfo startInfo = new ProcessStartInfo()
        {
            FileName = "cmd.exe",
            Arguments = $"/c {command}",
            UseShellExecute = false,
            CreateNoWindow = false,
            WorkingDirectory = Application.dataPath + "/..",
            RedirectStandardOutput = verboseLogging,
            RedirectStandardError = verboseLogging
        };

        trainingProcess = new Process();
        trainingProcess.StartInfo = startInfo;
        
        if (verboseLogging)
        {
            trainingProcess.OutputDataReceived += (sender, e) => {
                if (!string.IsNullOrEmpty(e.Data))
                    UnityEngine.Debug.Log($"[ML-Agents] {e.Data}");
            };
            
            trainingProcess.ErrorDataReceived += (sender, e) => {
                if (!string.IsNullOrEmpty(e.Data))
                    UnityEngine.Debug.LogError($"[ML-Agents Error] {e.Data}");
            };
        }

        trainingProcess.Start();
        
        if (verboseLogging)
        {
            trainingProcess.BeginOutputReadLine();
            trainingProcess.BeginErrorReadLine();
        }

        isTraining = true;
        UnityEngine.Debug.Log("✅ Training process started!");
        
        // Start TensorBoard if enabled
        if (enableTensorBoard)
        {
            Invoke(nameof(OpenTensorBoard), 5f); // Wait 5 seconds before opening TensorBoard
        }
    }

    void OnApplicationQuit()
    {
        StopTraining();
    }

    void OnDestroy()
    {
        StopTraining();
    }

    #region Public Utility Methods
    public void SetRunId(string newRunId)
    {
        runId = newRunId;
        UnityEngine.Debug.Log($"🏷️ Run ID set to: {runId}");
    }

    public void ToggleResumeTraining()
    {
        resumeTraining = !resumeTraining;
        UnityEngine.Debug.Log($"🔄 Resume training: {resumeTraining}");
    }

    public void SetMaxSteps(int steps)
    {
        maxSteps = steps;
        UnityEngine.Debug.Log($"📈 Max steps set to: {maxSteps}");
    }

    public bool IsTrainingActive()
    {
        return isTraining && trainingProcess != null && !trainingProcess.HasExited;
    }
    #endregion
}
