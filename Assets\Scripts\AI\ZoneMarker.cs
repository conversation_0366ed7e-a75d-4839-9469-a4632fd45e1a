using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Zone Marker System
/// Manages tactical zones for AI navigation and strategic positioning
/// </summary>
public class ZoneMarker : MonoBehaviour
{
    [Header("🎯 Zone Configuration")]
    public string zoneName = "Tactical Zone";
    public ZoneType zoneType = ZoneType.Patrol;
    public float zoneRadius = 10f;
    public int maxOccupants = 2;
    public float priorityScore = 1f;
    
    [Header("🎨 Visual Settings")]
    public Color zoneColor = Color.green;
    public bool showGizmos = true;
    public bool showZoneInfo = false;
    
    [Header("📊 Zone Status")]
    public List<VictorAgent> currentOccupants = new List<VictorAgent>();
    public TeamID controllingTeam = TeamID.TeamA;
    public float lastActivityTime = 0f;
    public bool isContested = false;
    
    [Header("🔗 Zone Connections")]
    public List<ZoneMarker> connectedZones = new List<ZoneMarker>();
    public List<FlankRoute> flankRoutes = new List<FlankRoute>();
    
    // Zone properties
    private SphereCollider zoneCollider;
    private Renderer zoneRenderer;
    private bool isInitialized = false;
    
    public enum ZoneType
    {
        Patrol,
        Defense,
        Attack,
        Flank,
        Cover,
        Overwatch,
        Fallback,
        Objective
    }
    
    void Start()
    {
        InitializeZone();
        RegisterWithZoneManager();
    }
    
    void InitializeZone()
    {
        // Create zone collider
        zoneCollider = GetComponent<SphereCollider>();
        if (zoneCollider == null)
        {
            zoneCollider = gameObject.AddComponent<SphereCollider>();
        }
        zoneCollider.isTrigger = true;
        zoneCollider.radius = zoneRadius;
        
        // Create visual representation
        CreateZoneVisual();
        
        // Initialize zone name if empty
        if (string.IsNullOrEmpty(zoneName))
        {
            zoneName = $"{zoneType}_Zone_{GetInstanceID()}";
        }
        
        gameObject.name = zoneName;
        isInitialized = true;
        
        Debug.Log($"🎯 Zone initialized: {zoneName} ({zoneType})");
    }
    
    void CreateZoneVisual()
    {
        // Create a visual indicator for the zone
        GameObject visual = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
        visual.transform.SetParent(transform);
        visual.transform.localPosition = Vector3.zero;
        visual.transform.localScale = new Vector3(zoneRadius * 2, 0.1f, zoneRadius * 2);
        visual.name = "ZoneVisual";
        
        // Remove collider from visual (we use our own)
        DestroyImmediate(visual.GetComponent<Collider>());
        
        zoneRenderer = visual.GetComponent<Renderer>();
        if (zoneRenderer != null)
        {
            Material zoneMaterial = new Material(Shader.Find("Standard"));
            zoneMaterial.color = zoneColor;
            zoneMaterial.SetFloat("_Mode", 3); // Transparent mode
            zoneMaterial.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
            zoneMaterial.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
            zoneMaterial.SetInt("_ZWrite", 0);
            zoneMaterial.DisableKeyword("_ALPHATEST_ON");
            zoneMaterial.EnableKeyword("_ALPHABLEND_ON");
            zoneMaterial.DisableKeyword("_ALPHAPREMULTIPLY_ON");
            zoneMaterial.renderQueue = 3000;
            
            Color transparentColor = zoneColor;
            transparentColor.a = 0.3f;
            zoneMaterial.color = transparentColor;
            
            zoneRenderer.material = zoneMaterial;
        }
    }
    
    void RegisterWithZoneManager()
    {
        ZoneManager zoneManager = FindObjectOfType<ZoneManager>();
        if (zoneManager != null)
        {
            zoneManager.RegisterZone(this);
        }
    }
    
    void OnTriggerEnter(Collider other)
    {
        VictorAgent agent = other.GetComponent<VictorAgent>();
        if (agent != null && !currentOccupants.Contains(agent))
        {
            if (currentOccupants.Count < maxOccupants)
            {
                currentOccupants.Add(agent);
                lastActivityTime = Time.time;
                
                // Notify agent of zone entry
                agent.OnZoneEntered(this);
                
                // Update zone control
                UpdateZoneControl();
                
                Debug.Log($"🎯 {agent.name} entered {zoneName}");
            }
        }
    }
    
    void OnTriggerExit(Collider other)
    {
        VictorAgent agent = other.GetComponent<VictorAgent>();
        if (agent != null && currentOccupants.Contains(agent))
        {
            currentOccupants.Remove(agent);
            
            // Notify agent of zone exit
            agent.OnZoneExited(this);
            
            // Update zone control
            UpdateZoneControl();
            
            Debug.Log($"🚪 {agent.name} left {zoneName}");
        }
    }
    
    void UpdateZoneControl()
    {
        if (currentOccupants.Count == 0)
        {
            isContested = false;
            return;
        }
        
        // Count agents by team
        int teamACount = 0;
        int teamBCount = 0;
        
        foreach (VictorAgent agent in currentOccupants)
        {
            if (agent.teamID == TeamID.TeamA)
                teamACount++;
            else
                teamBCount++;
        }
        
        // Determine control and contest status
        if (teamACount > 0 && teamBCount > 0)
        {
            isContested = true;
        }
        else
        {
            isContested = false;
            controllingTeam = (teamACount > teamBCount) ? TeamID.TeamA : TeamID.TeamB;
        }
        
        // Update visual based on control
        UpdateZoneVisual();
    }
    
    void UpdateZoneVisual()
    {
        if (zoneRenderer == null) return;
        
        Color targetColor = zoneColor;
        
        if (isContested)
        {
            targetColor = Color.yellow; // Contested zones are yellow
        }
        else if (currentOccupants.Count > 0)
        {
            targetColor = (controllingTeam == TeamID.TeamA) ? Color.blue : Color.red;
        }
        
        targetColor.a = 0.3f;
        zoneRenderer.material.color = targetColor;
    }
    
    public bool CanAcceptAgent(VictorAgent agent)
    {
        return currentOccupants.Count < maxOccupants && !currentOccupants.Contains(agent);
    }
    
    public bool IsControlledBy(TeamID team)
    {
        return !isContested && controllingTeam == team && currentOccupants.Count > 0;
    }
    
    public float GetDistanceTo(Vector3 position)
    {
        return Vector3.Distance(transform.position, position);
    }
    
    public Vector3 GetRandomPositionInZone()
    {
        Vector2 randomCircle = Random.insideUnitCircle * zoneRadius;
        Vector3 randomPosition = transform.position + new Vector3(randomCircle.x, 0, randomCircle.y);
        
        // Try to find a valid NavMesh position
        UnityEngine.AI.NavMeshHit hit;
        if (UnityEngine.AI.NavMesh.SamplePosition(randomPosition, out hit, zoneRadius, UnityEngine.AI.NavMesh.AllAreas))
        {
            return hit.position;
        }
        
        return transform.position;
    }
    
    public List<ZoneMarker> GetConnectedZones()
    {
        return new List<ZoneMarker>(connectedZones);
    }
    
    public void AddConnection(ZoneMarker otherZone)
    {
        if (!connectedZones.Contains(otherZone))
        {
            connectedZones.Add(otherZone);
            
            // Add bidirectional connection
            if (!otherZone.connectedZones.Contains(this))
            {
                otherZone.connectedZones.Add(this);
            }
            
            Debug.Log($"🔗 Connected {zoneName} to {otherZone.zoneName}");
        }
    }
    
    public void RemoveConnection(ZoneMarker otherZone)
    {
        connectedZones.Remove(otherZone);
        otherZone.connectedZones.Remove(this);
    }
    
    public ZoneMarker GetBestConnectedZone(VictorAgent agent)
    {
        ZoneMarker bestZone = null;
        float bestScore = float.MinValue;
        
        foreach (ZoneMarker zone in connectedZones)
        {
            if (zone.CanAcceptAgent(agent))
            {
                float score = CalculateZoneScore(zone, agent);
                if (score > bestScore)
                {
                    bestScore = score;
                    bestZone = zone;
                }
            }
        }
        
        return bestZone;
    }
    
    float CalculateZoneScore(ZoneMarker zone, VictorAgent agent)
    {
        float score = zone.priorityScore;
        
        // Distance penalty
        float distance = zone.GetDistanceTo(agent.transform.position);
        score -= distance * 0.01f;
        
        // Occupancy penalty
        score -= zone.currentOccupants.Count * 0.2f;
        
        // Role-specific bonuses
        AgentRoleComponent roleComponent = agent.GetComponent<AgentRoleComponent>();
        if (roleComponent != null)
        {
            switch (roleComponent.currentRole)
            {
                case "Scout":
                    if (zone.zoneType == ZoneType.Flank || zone.zoneType == ZoneType.Overwatch)
                        score += 0.5f;
                    break;
                case "Anchor":
                    if (zone.zoneType == ZoneType.Defense || zone.zoneType == ZoneType.Cover)
                        score += 0.5f;
                    break;
                case "Assault":
                    if (zone.zoneType == ZoneType.Attack || zone.zoneType == ZoneType.Objective)
                        score += 0.5f;
                    break;
            }
        }
        
        return score;
    }
    
    public void SetZoneType(ZoneType newType)
    {
        zoneType = newType;
        
        // Update color based on type
        switch (zoneType)
        {
            case ZoneType.Defense:
                zoneColor = Color.blue;
                break;
            case ZoneType.Attack:
                zoneColor = Color.red;
                break;
            case ZoneType.Flank:
                zoneColor = Color.yellow;
                break;
            case ZoneType.Cover:
                zoneColor = Color.gray;
                break;
            case ZoneType.Overwatch:
                zoneColor = Color.cyan;
                break;
            case ZoneType.Objective:
                zoneColor = Color.magenta;
                break;
            default:
                zoneColor = Color.green;
                break;
        }
        
        UpdateZoneVisual();
    }
    
    void OnDrawGizmosSelected()
    {
        if (!showGizmos) return;
        
        // Draw zone radius
        Gizmos.color = zoneColor;
        Gizmos.DrawWireSphere(transform.position, zoneRadius);
        
        // Draw connections
        Gizmos.color = Color.white;
        foreach (ZoneMarker connectedZone in connectedZones)
        {
            if (connectedZone != null)
            {
                Gizmos.DrawLine(transform.position, connectedZone.transform.position);
            }
        }
        
        // Draw flank routes
        Gizmos.color = Color.yellow;
        foreach (FlankRoute route in flankRoutes)
        {
            if (route != null)
            {
                route.DrawGizmos();
            }
        }
    }
    
    void OnGUI()
    {
        if (!showZoneInfo || !Application.isPlaying) return;
        
        Vector3 screenPos = Camera.main.WorldToScreenPoint(transform.position + Vector3.up * 2);
        if (screenPos.z > 0)
        {
            string info = $"{zoneName}\n{zoneType}\nOccupants: {currentOccupants.Count}/{maxOccupants}";
            if (isContested) info += "\nCONTESTED";
            
            GUI.Label(new Rect(screenPos.x - 50, Screen.height - screenPos.y - 30, 100, 60), info);
        }
    }
}
