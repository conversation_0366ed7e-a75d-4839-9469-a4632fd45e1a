using UnityEngine;
using UnityEditor;
using System.IO;

/// <summary>
/// Production Integration Setup
/// One-click setup for the complete Victor TDM system with decision tree integration
/// </summary>
public class ProductionIntegrationSetup : EditorWindow
{
    private Vector2 scrollPosition;
    
    [MenuItem("SquadMate AI/🚀 Production Integration Setup")]
    public static void ShowWindow()
    {
        GetWindow<ProductionIntegrationSetup>("Production Integration Setup");
    }
    
    void OnGUI()
    {
        scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
        
        GUILayout.Label("🚀 Production Integration Setup", EditorStyles.boldLabel);
        GUILayout.Space(10);
        
        GUILayout.Label("This will set up the complete Victor TDM system with:", EditorStyles.helpBox);
        GUILayout.Label("✅ AI Decision Tree Runtime System");
        GUILayout.Label("✅ Dynamic Role Assignment (Support/Assault/Scout/Anchor)");
        GUILayout.Label("✅ Tactical Zone Control System");
        GUILayout.Label("✅ Advanced Flanking Routes");
        GUILayout.Label("✅ Production Squad Manager Integration");
        
        GUILayout.Space(20);
        
        if (GUILayout.Button("🎮 Setup Complete Production System", GUILayout.Height(40)))
        {
            SetupCompleteSystem();
        }
        
        GUILayout.Space(10);
        
        GUILayout.Label("Individual Setup Options:", EditorStyles.boldLabel);
        
        if (GUILayout.Button("🧠 Setup AI Decision Tree System"))
        {
            SetupDecisionTreeSystem();
        }
        
        if (GUILayout.Button("🎭 Setup Role Assignment System"))
        {
            SetupRoleAssignmentSystem();
        }
        
        if (GUILayout.Button("🎯 Setup Zone Control System"))
        {
            SetupZoneControlSystem();
        }
        
        if (GUILayout.Button("🔄 Setup Flanking Route System"))
        {
            SetupFlankingRouteSystem();
        }
        
        if (GUILayout.Button("👥 Setup Production Squad Manager"))
        {
            SetupProductionSquadManager();
        }
        
        GUILayout.Space(20);
        
        GUILayout.Label("Validation & Testing:", EditorStyles.boldLabel);
        
        if (GUILayout.Button("🧪 Validate Complete System"))
        {
            ValidateCompleteSystem();
        }
        
        if (GUILayout.Button("📋 Generate Integration Report"))
        {
            GenerateIntegrationReport();
        }
        
        EditorGUILayout.EndScrollView();
    }
    
    void SetupCompleteSystem()
    {
        Debug.Log("🚀 Setting up complete production system...");
        
        SetupDecisionTreeSystem();
        SetupRoleAssignmentSystem();
        SetupZoneControlSystem();
        SetupFlankingRouteSystem();
        SetupProductionSquadManager();
        
        Debug.Log("✅ Complete production system setup finished!");
        EditorUtility.DisplayDialog("Success", "Complete production system setup finished!", "OK");
    }
    
    void SetupDecisionTreeSystem()
    {
        Debug.Log("🧠 Setting up AI Decision Tree System...");
        
        // Create AI Decision Tree Reader
        GameObject decisionTreeObj = GameObject.Find("AIDecisionTreeReader");
        if (decisionTreeObj == null)
        {
            decisionTreeObj = new GameObject("AIDecisionTreeReader");
            decisionTreeObj.AddComponent<AIDecisionTreeReader>();
        }
        
        // Assign decision tree JSON if available
        AIDecisionTreeReader reader = decisionTreeObj.GetComponent<AIDecisionTreeReader>();
        if (reader != null)
        {
            // Try to find the decision tree JSON file
            string[] guids = AssetDatabase.FindAssets("ai_decision_tree_production");
            if (guids.Length > 0)
            {
                string path = AssetDatabase.GUIDToAssetPath(guids[0]);
                TextAsset jsonAsset = AssetDatabase.LoadAssetAtPath<TextAsset>(path);
                reader.decisionTreeJson = jsonAsset;
                Debug.Log($"✅ Assigned decision tree JSON: {path}");
            }
        }
        
        Debug.Log("✅ AI Decision Tree System setup complete");
    }
    
    void SetupRoleAssignmentSystem()
    {
        Debug.Log("🎭 Setting up Role Assignment System...");
        
        // Create Role Assigner
        GameObject roleAssignerObj = GameObject.Find("RoleAssigner");
        if (roleAssignerObj == null)
        {
            roleAssignerObj = new GameObject("RoleAssigner");
            roleAssignerObj.AddComponent<RoleAssigner>();
        }
        
        // Link to decision tree reader
        RoleAssigner roleAssigner = roleAssignerObj.GetComponent<RoleAssigner>();
        AIDecisionTreeReader decisionTreeReader = FindObjectOfType<AIDecisionTreeReader>();
        if (roleAssigner != null && decisionTreeReader != null)
        {
            roleAssigner.decisionTreeReader = decisionTreeReader;
        }
        
        Debug.Log("✅ Role Assignment System setup complete");
    }
    
    void SetupZoneControlSystem()
    {
        Debug.Log("🎯 Setting up Zone Control System...");
        
        // Create Zone Manager
        GameObject zoneManagerObj = GameObject.Find("ZoneManager");
        if (zoneManagerObj == null)
        {
            zoneManagerObj = new GameObject("ZoneManager");
            zoneManagerObj.AddComponent<ZoneManager>();
        }
        
        // Create sample tactical zones
        CreateSampleZones();
        
        Debug.Log("✅ Zone Control System setup complete");
    }
    
    void CreateSampleZones()
    {
        GameObject zoneParent = GameObject.Find("TacticalZones");
        if (zoneParent == null)
        {
            zoneParent = new GameObject("TacticalZones");
        }
        
        // Create 5 tactical zones
        Vector3[] zonePositions = {
            new Vector3(0, 0, 0),      // Center - Objective
            new Vector3(-15, 0, 15),   // North West - Defense
            new Vector3(15, 0, 15),    // North East - Attack
            new Vector3(-15, 0, -15),  // South West - Flank
            new Vector3(15, 0, -15)    // South East - Overwatch
        };
        
        ZoneMarker.ZoneType[] zoneTypes = {
            ZoneMarker.ZoneType.Objective,
            ZoneMarker.ZoneType.Defense,
            ZoneMarker.ZoneType.Attack,
            ZoneMarker.ZoneType.Flank,
            ZoneMarker.ZoneType.Overwatch
        };
        
        for (int i = 0; i < zonePositions.Length; i++)
        {
            GameObject zoneObj = new GameObject($"TacticalZone_{i}_{zoneTypes[i]}");
            zoneObj.transform.SetParent(zoneParent.transform);
            zoneObj.transform.position = zonePositions[i];
            
            ZoneMarker zoneMarker = zoneObj.AddComponent<ZoneMarker>();
            zoneMarker.zoneName = $"Zone {i + 1}";
            zoneMarker.zoneType = zoneTypes[i];
            zoneMarker.zoneRadius = 8f;
            zoneMarker.priorityScore = 1f + (i * 0.2f);
        }
        
        Debug.Log("✅ Created 5 tactical zones");
    }
    
    void SetupFlankingRouteSystem()
    {
        Debug.Log("🔄 Setting up Flanking Route System...");
        
        // Create Flank Route Manager
        GameObject routeManagerObj = GameObject.Find("FlankRouteManager");
        if (routeManagerObj == null)
        {
            routeManagerObj = new GameObject("FlankRouteManager");
            routeManagerObj.AddComponent<FlankRouteManager>();
        }
        
        // Create sample flank routes
        CreateSampleFlankRoutes();
        
        Debug.Log("✅ Flanking Route System setup complete");
    }
    
    void CreateSampleFlankRoutes()
    {
        GameObject routeParent = GameObject.Find("FlankRoutes");
        if (routeParent == null)
        {
            routeParent = new GameObject("FlankRoutes");
        }
        
        // Create 3 flank routes
        CreateFlankRoute("LeftFlankRoute", routeParent.transform, 
            new Vector3(-25, 0, 0), new Vector3(-20, 0, 10), new Vector3(-10, 0, 20), new Vector3(0, 0, 25));
        
        CreateFlankRoute("RightFlankRoute", routeParent.transform,
            new Vector3(25, 0, 0), new Vector3(20, 0, 10), new Vector3(10, 0, 20), new Vector3(0, 0, 25));
        
        CreateFlankRoute("CenterAdvanceRoute", routeParent.transform,
            new Vector3(0, 0, -25), new Vector3(0, 0, -10), new Vector3(0, 0, 10), new Vector3(0, 0, 25));
        
        Debug.Log("✅ Created 3 flank routes");
    }
    
    void CreateFlankRoute(string routeName, Transform parent, params Vector3[] waypoints)
    {
        GameObject routeObj = new GameObject(routeName);
        routeObj.transform.SetParent(parent);
        
        FlankRoute flankRoute = routeObj.AddComponent<FlankRoute>();
        flankRoute.routeName = routeName;
        flankRoute.routeType = FlankRoute.RouteType.Flank;
        
        // Create waypoint objects
        for (int i = 0; i < waypoints.Length; i++)
        {
            GameObject waypointObj = new GameObject($"Waypoint_{i}");
            waypointObj.transform.SetParent(routeObj.transform);
            waypointObj.transform.position = waypoints[i];
            
            if (i == 0)
                flankRoute.startPoint = waypointObj.transform;
            else if (i == waypoints.Length - 1)
                flankRoute.endPoint = waypointObj.transform;
            else
                flankRoute.waypoints.Add(waypointObj.transform);
        }
    }
    
    void SetupProductionSquadManager()
    {
        Debug.Log("👥 Setting up Production Squad Manager...");
        
        // Find or create Squad Manager
        SquadManager squadManager = FindObjectOfType<SquadManager>();
        if (squadManager == null)
        {
            GameObject squadManagerObj = new GameObject("SquadManager");
            squadManager = squadManagerObj.AddComponent<SquadManager>();
        }
        
        // Create spawn points if they don't exist
        CreateSpawnPoints();
        
        Debug.Log("✅ Production Squad Manager setup complete");
    }
    
    void CreateSpawnPoints()
    {
        // Create Team A spawn points
        GameObject teamASpawns = GameObject.Find("TeamA_Spawns");
        if (teamASpawns == null)
        {
            teamASpawns = new GameObject("TeamA_Spawns");
            
            Vector3[] teamAPositions = {
                new Vector3(-30, 0, 30),
                new Vector3(-25, 0, 30),
                new Vector3(-30, 0, 25),
                new Vector3(-25, 0, 25),
                new Vector3(-27.5f, 0, 27.5f)
            };
            
            for (int i = 0; i < teamAPositions.Length; i++)
            {
                GameObject spawn = new GameObject($"TeamA_Spawn_{i}");
                spawn.transform.SetParent(teamASpawns.transform);
                spawn.transform.position = teamAPositions[i];
            }
        }
        
        // Create Team B spawn points
        GameObject teamBSpawns = GameObject.Find("TeamB_Spawns");
        if (teamBSpawns == null)
        {
            teamBSpawns = new GameObject("TeamB_Spawns");
            
            Vector3[] teamBPositions = {
                new Vector3(30, 0, -30),
                new Vector3(25, 0, -30),
                new Vector3(30, 0, -25),
                new Vector3(25, 0, -25),
                new Vector3(27.5f, 0, -27.5f)
            };
            
            for (int i = 0; i < teamBPositions.Length; i++)
            {
                GameObject spawn = new GameObject($"TeamB_Spawn_{i}");
                spawn.transform.SetParent(teamBSpawns.transform);
                spawn.transform.position = teamBPositions[i];
            }
        }
        
        Debug.Log("✅ Created spawn points for both teams");
    }
    
    void ValidateCompleteSystem()
    {
        Debug.Log("🧪 Validating complete system...");
        
        var issues = new System.Collections.Generic.List<string>();
        
        // Check for required components
        if (FindObjectOfType<AIDecisionTreeReader>() == null)
            issues.Add("❌ AIDecisionTreeReader not found");
        
        if (FindObjectOfType<RoleAssigner>() == null)
            issues.Add("❌ RoleAssigner not found");
        
        if (FindObjectOfType<ZoneManager>() == null)
            issues.Add("❌ ZoneManager not found");
        
        if (FindObjectOfType<FlankRouteManager>() == null)
            issues.Add("❌ FlankRouteManager not found");
        
        if (FindObjectOfType<SquadManager>() == null)
            issues.Add("❌ SquadManager not found");
        
        // Check for zones and routes
        ZoneMarker[] zones = FindObjectsOfType<ZoneMarker>();
        if (zones.Length == 0)
            issues.Add("❌ No tactical zones found");
        
        FlankRoute[] routes = FindObjectsOfType<FlankRoute>();
        if (routes.Length == 0)
            issues.Add("❌ No flank routes found");
        
        // Display results
        if (issues.Count == 0)
        {
            Debug.Log("✅ System validation passed!");
            EditorUtility.DisplayDialog("Validation Success", "All systems are properly configured!", "OK");
        }
        else
        {
            string issueList = string.Join("\n", issues);
            Debug.LogWarning($"⚠️ Validation issues:\n{issueList}");
            EditorUtility.DisplayDialog("Validation Issues", $"Issues found:\n{issueList}", "OK");
        }
    }
    
    void GenerateIntegrationReport()
    {
        Debug.Log("📋 Generating integration report...");
        
        string report = "# Production Integration Report\n\n";
        report += $"Generated: {System.DateTime.Now}\n\n";
        
        // System status
        report += "## System Status\n";
        report += $"- AIDecisionTreeReader: {(FindObjectOfType<AIDecisionTreeReader>() != null ? "✅" : "❌")}\n";
        report += $"- RoleAssigner: {(FindObjectOfType<RoleAssigner>() != null ? "✅" : "❌")}\n";
        report += $"- ZoneManager: {(FindObjectOfType<ZoneManager>() != null ? "✅" : "❌")}\n";
        report += $"- FlankRouteManager: {(FindObjectOfType<FlankRouteManager>() != null ? "✅" : "❌")}\n";
        report += $"- SquadManager: {(FindObjectOfType<SquadManager>() != null ? "✅" : "❌")}\n\n";
        
        // Scene analysis
        ZoneMarker[] zones = FindObjectsOfType<ZoneMarker>();
        FlankRoute[] routes = FindObjectsOfType<FlankRoute>();
        VictorAgent[] agents = FindObjectsOfType<VictorAgent>();
        
        report += "## Scene Analysis\n";
        report += $"- Tactical Zones: {zones.Length}\n";
        report += $"- Flank Routes: {routes.Length}\n";
        report += $"- Victor Agents: {agents.Length}\n\n";
        
        // Save report
        string path = "Production_Integration_Report.md";
        File.WriteAllText(path, report);
        
        Debug.Log($"✅ Integration report saved to {path}");
        EditorUtility.DisplayDialog("Report Generated", $"Integration report saved to {path}", "OK");
    }
}
