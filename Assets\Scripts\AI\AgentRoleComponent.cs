using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Agent Role Component
/// Applies and manages individual agent role behaviors
/// </summary>
public class AgentRoleComponent : MonoBehaviour
{
    [Header("🎭 Current Role")]
    public string currentRole = "Support";
    public float roleEffectiveness = 1f;
    public bool isRoleActive = true;
    
    [Header("📊 Role Performance")]
    public float reviveCount = 0f;
    public float killCount = 0f;
    public float zoneCaptureCount = 0f;
    public float flankingSuccessCount = 0f;
    public float teamProximityTime = 0f;
    public float survivalTime = 0f;
    public float zoneDefenseTime = 0f;
    public float enemySpottedCount = 0f;
    
    [Header("⚙️ Role Modifiers")]
    public float combatAggression = 0.5f;
    public float teamCoordinationWeight = 0.7f;
    public float explorationTendency = 0.5f;
    public float survivalPriority = 0.6f;
    
    // Role-specific behaviors
    private RoleDefinition currentRoleDefinition;
    private VictorAgent victorAgent;
    private AIDecisionTreeReader decisionTreeReader;
    private float roleStartTime;
    
    // Role state tracking
    private Vector3 lastTeamPosition;
    private float lastProximityCheck;
    private bool isInZone = false;
    private float zoneEntryTime;
    
    void Start()
    {
        victorAgent = GetComponent<VictorAgent>();
        decisionTreeReader = FindObjectOfType<AIDecisionTreeReader>();
        roleStartTime = Time.time;
        
        // Initialize performance tracking
        InvokeRepeating(nameof(UpdatePerformanceMetrics), 1f, 1f);
    }
    
    public void ApplyRole(RoleDefinition roleDefinition)
    {
        currentRoleDefinition = roleDefinition;
        currentRole = roleDefinition.roleName;
        roleStartTime = Time.time;
        
        // Apply behavior modifiers
        if (roleDefinition.behaviorModifiers != null)
        {
            foreach (var modifier in roleDefinition.behaviorModifiers)
            {
                ApplyBehaviorModifier(modifier.Key, modifier.Value);
            }
        }
        
        // Update agent priorities
        UpdateAgentPriorities();
        
        Debug.Log($"🎭 {gameObject.name} now playing {currentRole} role");
    }
    
    void ApplyBehaviorModifier(string modifierType, float value)
    {
        switch (modifierType)
        {
            case "aggression":
                combatAggression = value;
                break;
            case "teamwork":
                teamCoordinationWeight = value;
                break;
            case "survival":
                survivalPriority = value;
                break;
            case "exploration":
                explorationTendency = value;
                break;
        }
    }
    
    void UpdateAgentPriorities()
    {
        if (victorAgent == null || currentRoleDefinition == null) return;
        
        // Update tactical priorities based on role
        victorAgent.tacticalPriorities = currentRoleDefinition.priorities;
        
        // Update combat behavior
        victorAgent.combatAggression = combatAggression;
        victorAgent.teamCoordinationWeight = teamCoordinationWeight;
        
        // Role-specific behavior updates
        switch (currentRole)
        {
            case "Support":
                ApplySupportBehavior();
                break;
            case "Assault":
                ApplyAssaultBehavior();
                break;
            case "Scout":
                ApplyScoutBehavior();
                break;
            case "Anchor":
                ApplyAnchorBehavior();
                break;
        }
    }
    
    void ApplySupportBehavior()
    {
        // Support agents prioritize team health and revives
        victorAgent.reviveRange *= 1.5f; // Increased revive range
        victorAgent.followDistance = 8f; // Stay closer to team
        
        // Prefer healing items
        PUBGInventory inventory = GetComponent<PUBGInventory>();
        if (inventory != null)
        {
            inventory.prioritizeHealing = true;
        }
    }
    
    void ApplyAssaultBehavior()
    {
        // Assault agents are more aggressive
        victorAgent.engagementRange *= 1.2f; // Engage at longer range
        victorAgent.followDistance = 12f; // Lead the team
        
        // Prefer combat items
        WeaponSystem weaponSystem = GetComponent<WeaponSystem>();
        if (weaponSystem != null)
        {
            weaponSystem.preferCombatWeapons = true;
        }
    }
    
    void ApplyScoutBehavior()
    {
        // Scout agents explore and flank
        victorAgent.followDistance = 20f; // Operate independently
        explorationTendency = 0.9f;
        
        // Faster movement
        UnityEngine.AI.NavMeshAgent navAgent = GetComponent<UnityEngine.AI.NavMeshAgent>();
        if (navAgent != null)
        {
            navAgent.speed *= 1.2f;
        }
    }
    
    void ApplyAnchorBehavior()
    {
        // Anchor agents hold positions
        victorAgent.followDistance = 5f; // Stay close to zones
        survivalPriority = 0.9f;
        
        // Prefer defensive positions
        victorAgent.preferDefensivePositions = true;
    }
    
    void UpdatePerformanceMetrics()
    {
        if (victorAgent == null) return;
        
        // Update survival time
        if (victorAgent.currentHealth > 0)
        {
            survivalTime += 1f;
        }
        
        // Update team proximity time
        UpdateTeamProximityMetrics();
        
        // Update zone metrics
        UpdateZoneMetrics();
        
        // Calculate role effectiveness
        CalculateRoleEffectiveness();
    }
    
    void UpdateTeamProximityMetrics()
    {
        if (victorAgent.squadMembers == null || victorAgent.squadMembers.Count == 0) return;
        
        float averageDistance = 0f;
        int validMembers = 0;
        
        foreach (VictorAgent member in victorAgent.squadMembers)
        {
            if (member != null && member != victorAgent)
            {
                averageDistance += Vector3.Distance(transform.position, member.transform.position);
                validMembers++;
            }
        }
        
        if (validMembers > 0)
        {
            averageDistance /= validMembers;
            
            // Consider "good proximity" as being within 15 units of team
            if (averageDistance <= 15f)
            {
                teamProximityTime += 1f;
            }
        }
    }
    
    void UpdateZoneMetrics()
    {
        // Check if agent is in a capture zone
        CaptureZone[] zones = FindObjectsOfType<CaptureZone>();
        bool currentlyInZone = false;
        
        foreach (CaptureZone zone in zones)
        {
            float distance = Vector3.Distance(transform.position, zone.transform.position);
            if (distance <= zone.captureRadius)
            {
                currentlyInZone = true;
                
                if (!isInZone)
                {
                    // Just entered zone
                    isInZone = true;
                    zoneEntryTime = Time.time;
                }
                else
                {
                    // Defending zone
                    zoneDefenseTime += 1f;
                }
                break;
            }
        }
        
        if (!currentlyInZone && isInZone)
        {
            // Just left zone
            isInZone = false;
        }
    }
    
    void CalculateRoleEffectiveness()
    {
        float effectiveness = 0.5f; // Base effectiveness
        
        switch (currentRole)
        {
            case "Support":
                // Support effectiveness based on revives and team proximity
                effectiveness += (reviveCount * 0.2f) + (teamProximityTime / 100f);
                break;
                
            case "Assault":
                // Assault effectiveness based on kills and zone captures
                effectiveness += (killCount * 0.3f) + (zoneCaptureCount * 0.4f);
                break;
                
            case "Scout":
                // Scout effectiveness based on flanking and exploration
                effectiveness += (flankingSuccessCount * 0.4f) + (enemySpottedCount * 0.1f);
                break;
                
            case "Anchor":
                // Anchor effectiveness based on zone defense and survival
                effectiveness += (zoneDefenseTime / 50f) + (survivalTime / 100f);
                break;
        }
        
        roleEffectiveness = Mathf.Clamp01(effectiveness);
    }
    
    public string GetRoleAction(string state, string condition)
    {
        if (decisionTreeReader != null)
        {
            return decisionTreeReader.GetAction(state, condition);
        }
        
        // Fallback to role-specific default actions
        return GetRoleSpecificAction(state, condition);
    }
    
    string GetRoleSpecificAction(string state, string condition)
    {
        switch (currentRole)
        {
            case "Support":
                return GetSupportAction(state, condition);
            case "Assault":
                return GetAssaultAction(state, condition);
            case "Scout":
                return GetScoutAction(state, condition);
            case "Anchor":
                return GetAnchorAction(state, condition);
            default:
                return "patrol_nearest_zone";
        }
    }
    
    string GetSupportAction(string state, string condition)
    {
        switch (state)
        {
            case "player_down":
                return "deploy_smoke_then_revive";
            case "low_health":
                return condition == "has_medkit" ? "use_medkit" : "fallback_and_loot";
            case "enemy_detected":
                return "seek_cover_and_call_backup";
            default:
                return "follow_squad_leader";
        }
    }
    
    string GetAssaultAction(string state, string condition)
    {
        switch (state)
        {
            case "enemy_detected":
                return "engage_or_flank_based_on_position";
            case "zone_control":
                return "push_and_flank_to_retake";
            default:
                return "patrol_nearest_zone";
        }
    }
    
    string GetScoutAction(string state, string condition)
    {
        switch (state)
        {
            case "enemy_detected":
                return "flank_and_spot";
            case "idle":
                return "explore_and_gather_intel";
            default:
                return "patrol_perimeter";
        }
    }
    
    string GetAnchorAction(string state, string condition)
    {
        switch (state)
        {
            case "zone_control":
                return "hold_position_and_defend";
            case "enemy_detected":
                return "suppress_and_hold";
            default:
                return "patrol_nearest_zone";
        }
    }
    
    public void OnReviveSuccess()
    {
        reviveCount++;
        
        if (decisionTreeReader != null)
        {
            float reward = decisionTreeReader.GetReward("revive_success");
            victorAgent?.AddReward(reward);
        }
    }
    
    public void OnKillEnemy()
    {
        killCount++;
        
        if (currentRole == "Assault")
        {
            // Bonus reward for assault role
            victorAgent?.AddReward(0.1f);
        }
    }
    
    public void OnZoneCapture()
    {
        zoneCaptureCount++;
        
        if (currentRole == "Assault" || currentRole == "Anchor")
        {
            // Bonus reward for zone-focused roles
            victorAgent?.AddReward(0.15f);
        }
    }
    
    public void OnFlankingSuccess()
    {
        flankingSuccessCount++;
        
        if (currentRole == "Scout")
        {
            // Bonus reward for scout role
            victorAgent?.AddReward(0.2f);
        }
    }
    
    public void OnEnemySpotted()
    {
        enemySpottedCount++;
        
        if (currentRole == "Scout")
        {
            // Small reward for intel gathering
            victorAgent?.AddReward(0.05f);
        }
    }
    
    public RoleDefinition GetCurrentRoleDefinition()
    {
        return currentRoleDefinition;
    }
    
    public float GetTimeSinceRoleAssignment()
    {
        return Time.time - roleStartTime;
    }
}
