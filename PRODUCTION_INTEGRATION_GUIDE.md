# 🎮 Production-Ready Victor TDM Integration Guide

## 🚀 **Complete System Overview**

Your Victor 5v5 TDM system now includes:

### 🧠 **AI Decision Tree System**
- ✅ **AIDecisionTreeReader.cs** - Runtime JSON decision tree loader
- ✅ **ai_decision_tree_production.json** - Production decision tree
- ✅ **Hot-reload support** - Dynamic behavior updates during training

### 🎭 **Role Assignment System**
- ✅ **RoleAssigner.cs** - Dynamic role management (Support/Assault/Scout/Anchor)
- ✅ **AgentRoleComponent.cs** - Individual agent role behaviors
- ✅ **Performance-based role swapping** - Agents adapt based on effectiveness

### 🎯 **Zone Control System**
- ✅ **ZoneMarker.cs** - Tactical zone management with visual feedback
- ✅ **ZoneManager.cs** - Global zone control coordination
- ✅ **Zone types**: Patrol, Defense, Attack, Flank, Cover, Overwatch, Fallback, Objective

### 🔄 **Flanking Route System**
- ✅ **FlankRoute.cs** - Dynamic flanking path management
- ✅ **FlankRouteManager.cs** - Route optimization and blocking detection
- ✅ **Route types**: Flank, Retreat, Advance, Patrol, Escape, Ambush

### 👥 **Enhanced Squad Management**
- ✅ **Production SquadManager.cs** - Compatible with your existing system
- ✅ **5v5 team spawning** with role assignment
- ✅ **Team coordination** and tactical objectives

---

## 🛠️ **Setup Instructions**

### **1. Quick Setup (Recommended)**
```
1. Use Unity Editor: SquadMate AI → 🎮 Victor TDM Setup
2. Click: 🎮 Create Complete TDM Scene
3. The system will automatically:
   - Setup Victor agents with all components
   - Create tactical zones and flank routes
   - Configure role assignment system
   - Load decision tree from JSON
```

### **2. Manual Integration**
```csharp
// Add to your scene:
1. AIDecisionTreeReader (assign ai_decision_tree_production.json)
2. RoleAssigner (links to decision tree reader)
3. ZoneManager (manages all tactical zones)
4. FlankRouteManager (manages flanking routes)

// Each Victor agent needs:
- VictorAgent.cs (extends SquadMateAgent)
- AgentRoleComponent.cs (role-specific behaviors)
- All existing PUBG systems (inventory, armor, weapons, health)
```

---

## 🎯 **How the System Works**

### **🧠 Decision Tree Integration**
```csharp
// Agents read decisions from JSON:
string action = decisionTreeReader.GetAction("enemy_detected", "in_squad");
// Returns: "engage_or_flank_based_on_position"

// Role-specific rewards:
float reward = decisionTreeReader.GetReward("flank_success");
agent.AddReward(reward); // +0.5 for successful flanking
```

### **🎭 Dynamic Role Assignment**
```csharp
// Automatic role assignment based on team composition:
Team Composition (5v5):
- 2x Support (revive, healing, team coordination)
- 1x Assault (aggressive push, zone capture)
- 1x Scout (flanking, intel gathering)
- 1x Anchor (zone defense, overwatch)

// Performance-based role swapping:
if (agent.performance < 0.3f && timeSinceAssignment > 30f)
{
    // Reassign to better-suited role
    roleAssigner.ConsiderRoleSwap(agent);
}
```

### **🎯 Zone Control Mechanics**
```csharp
// Zones provide tactical objectives:
- Capture zones for team bonuses (+0.1 reward/frame)
- Contest enemy zones for tactical advantage
- Role-specific zone preferences (Scout → Flank zones)
- Dynamic zone connections for pathfinding
```

### **🔄 Flanking Route System**
```csharp
// Smart route selection:
FlankRoute bestRoute = routeManager.GetBestRoute(agent, targetPosition, RouteType.Flank);
if (bestRoute.CanUseRoute(agent))
{
    agent.StartUsingRoute(bestRoute);
    Vector3 nextWaypoint = bestRoute.GetNextWaypoint(agent, agent.transform.position);
}
```

---

## 📊 **Training Configuration**

### **Enhanced Training Config**
```yaml
behaviors:
  VictorTDM:
    trainer_type: poca  # Multi-agent training
    hyperparameters:
      batch_size: 2048
      buffer_size: 20480
      learning_rate: 3e-4
      beta: 5e-3
      epsilon: 0.2
      lambd: 0.95
      num_epoch: 3

    network_settings:
      normalize: true
      hidden_units: 512
      num_layers: 3
      memory:
        sequence_length: 64
        memory_size: 256

    reward_signals:
      extrinsic:
        gamma: 0.99
        strength: 1.0
      curiosity:
        gamma: 0.99
        strength: 0.02

    self_play:
      save_steps: 50000
      team_change: 200000
      swap_steps: 10000
      play_against_latest_model_ratio: 0.5

    max_steps: 5000000
    time_horizon: 1000
    summary_freq: 10000
```

### **Advanced Reward Structure**
```json
{
  "individual_rewards": {
    "enemy_elimination": 1.0,
    "revive_success": 0.4,
    "zone_capture": 0.3,
    "flank_success": 0.5,
    "loot_high_tier": 0.3
  },
  "team_rewards": {
    "zone_control": 0.1,
    "team_coordination": 0.2,
    "tactical_positioning": 0.15,
    "squad_following": 0.05
  },
  "role_specific": {
    "support_revive_bonus": 0.2,
    "assault_zone_bonus": 0.15,
    "scout_flank_bonus": 0.25,
    "anchor_defense_bonus": 0.2
  }
}
```

---

## 🎮 **Runtime Features**

### **🔄 Hot-Reload Decision Tree**
```csharp
// Modify ai_decision_tree_production.json during training
// System automatically reloads within 2 seconds
// Test different behaviors without stopping training
```

### **📊 Real-Time Monitoring**
```csharp
// In-game UI displays:
- Team role assignments and performance
- Zone control status (Team A: 3, Team B: 2, Contested: 1)
- Active flank routes and usage
- Decision tree status and loaded actions
```

### **🎯 Tactical Objectives**
```csharp
// Dynamic objectives based on match state:
- Early game: Loot collection and positioning
- Mid game: Zone control and team coordination
- Late game: Tactical flanking and elimination
```

---

## 🧪 **Testing & Validation**

### **System Validation Checklist**
```
✅ AIDecisionTreeReader loads JSON successfully
✅ RoleAssigner assigns 5 roles per team (2 Support, 1 Assault, 1 Scout, 1 Anchor)
✅ ZoneManager tracks zone control and awards rewards
✅ FlankRouteManager provides tactical routing
✅ Agents respond to decision tree actions
✅ Role performance tracking works
✅ Zone capture mechanics function
✅ Flank routes update based on enemy positions
```

### **Performance Metrics**
```csharp
// Monitor these during training:
- Role effectiveness scores (target: >0.7)
- Zone control percentage (balanced teams: ~50/50)
- Flank route usage (scouts should use 80%+ of available routes)
- Team coordination time (agents within 15 units: >60%)
- Decision tree action diversity (avoid repetitive behaviors)
```

---

## 🎯 **Expected Training Results**

### **Phase 1: Role Learning (0-1M steps)**
- ✅ Agents learn basic role behaviors
- ✅ Support agents prioritize revives
- ✅ Assault agents push objectives
- ✅ Scouts explore and flank
- ✅ Anchors hold defensive positions

### **Phase 2: Team Coordination (1M-3M steps)**
- ✅ Coordinated zone captures
- ✅ Tactical flanking maneuvers
- ✅ Role-based decision making
- ✅ Dynamic role adaptation

### **Phase 3: Advanced Tactics (3M+ steps)**
- ✅ Complex multi-agent strategies
- ✅ Adaptive role swapping
- ✅ Sophisticated zone control
- ✅ Professional-level team play

---

## 🔧 **Troubleshooting**

### **Common Issues**

**Decision tree not loading:**
```
1. Check ai_decision_tree_production.json is in project root
2. Verify JSON syntax is valid
3. Assign TextAsset in AIDecisionTreeReader inspector
```

**Roles not assigning:**
```
1. Ensure RoleAssigner is in scene
2. Check SquadManager has Victor agents
3. Verify AgentRoleComponent is added to agents
```

**Zones not working:**
```
1. Create ZoneMarker objects in scene
2. Add ZoneManager to scene
3. Set zone types and radii appropriately
```

**Routes not functioning:**
```
1. Create FlankRoute objects with waypoints
2. Add FlankRouteManager to scene
3. Connect routes to zones
```

---

## 🎉 **Success Indicators**

**Your production system is working when:**
- ✅ 10 Victor agents spawn with assigned roles
- ✅ Decision tree actions execute based on game state
- ✅ Agents capture and defend zones tactically
- ✅ Flanking routes are used strategically
- ✅ Role performance adapts over time
- ✅ Team coordination emerges naturally
- ✅ Complex tactical behaviors develop

---

**🎮 Your Victor TDM system is now production-ready with advanced AI decision-making, dynamic role assignment, tactical zone control, and sophisticated flanking mechanics!**

The system provides a comprehensive foundation for training professional-level AI agents that can compete in complex team-based scenarios.
