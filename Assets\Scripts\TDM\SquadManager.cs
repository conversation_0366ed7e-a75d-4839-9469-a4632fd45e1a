using UnityEngine;
using Unity.MLAgents;
using System.Collections.Generic;
using System.Collections;

/// <summary>
/// 5v5 Team Deathmatch Squad Manager for Victor AI agents
/// Manages team spawning, objectives, and tactical coordination with role assignment
/// </summary>
public class SquadManager : MonoBehaviour
{
    [Header("🎮 5v5 TDM Configuration")]
    public int teamSize = 5;
    public float matchDuration = 600f; // 10 minutes
    public int scoreToWin = 50;

    [Header("🧍‍♂️ Team Prefabs")]
    public GameObject squadmatePrefab;
    public GameObject enemyBotPrefab;

    [Header("🗺️ TDM Arena Setup")]
    public Transform[] squadSpawns;
    public Transform[] enemySpawns;
    public Transform[] mapZones;
    public Transform[] captureZones;
    public Transform[] tacticalPositions;

    [Header("🎯 Team Management")]
    public List<VictorAgent> teamA = new List<VictorAgent>();
    public List<VictorAgent> teamB = new List<VictorAgent>();

    // Production arrays for compatibility
    private GameObject[] squadMates;
    private GameObject[] enemies;

    [Header("📊 Match Statistics")]
    public int teamAScore = 0;
    public int teamBScore = 0;
    public float matchTimer = 0f;
    public bool matchActive = false;

    [Header("🎮 Training Settings")]
    public bool enableSelfPlay = true;
    public bool enableCurriculumLearning = true;
    public float difficultyLevel = 1f;

    // Events
    public System.Action<int> OnTeamScored;
    public System.Action<int> OnMatchEnded;

    private TDMEnvironment tdmEnvironment;
    private List<GameObject> activeAgents = new List<GameObject>();

    void Start()
    {
        InitializeTDMArena();
        SetupTeams();
        SetupProductionTeams();
        StartMatch();
    }

    void InitializeTDMArena()
    {
        Debug.Log("🗺️ Initializing TDM Arena...");

        // Get or create TDM Environment
        tdmEnvironment = GetComponent<TDMEnvironment>();
        if (tdmEnvironment == null)
        {
            tdmEnvironment = gameObject.AddComponent<TDMEnvironment>();
        }

        // Validate spawn points
        if (teamASpawnPoints.Length < teamSize)
        {
            Debug.LogWarning($"⚠️ Not enough Team A spawn points! Need {teamSize}, have {teamASpawnPoints.Length}");
        }

        if (teamBSpawnPoints.Length < teamSize)
        {
            Debug.LogWarning($"⚠️ Not enough Team B spawn points! Need {teamSize}, have {teamBSpawnPoints.Length}");
        }

        Debug.Log("✅ TDM Arena initialized");
    }

    void SetupTeams()
    {
        Debug.Log("👥 Setting up 5v5 teams...");

        // Clear existing teams
        ClearTeams();

        // Create Team A (Blue)
        for (int i = 0; i < teamSize; i++)
        {
            Vector3 spawnPos = GetSpawnPosition(teamASpawnPoints, i);
            VictorAgent agent = CreateVictorAgent($"Victor_TeamA_{i}", spawnPos, TeamID.TeamA);
            teamA.Add(agent);
            activeAgents.Add(agent.gameObject);
        }

        // Create Team B (Red)
        for (int i = 0; i < teamSize; i++)
        {
            Vector3 spawnPos = GetSpawnPosition(teamBSpawnPoints, i);
            VictorAgent agent = CreateVictorAgent($"Victor_TeamB_{i}", spawnPos, TeamID.TeamB);
            teamB.Add(agent);
            activeAgents.Add(agent.gameObject);
        }

        Debug.Log($"✅ Teams created: Team A ({teamA.Count}) vs Team B ({teamB.Count})");
    }

    void SetupProductionTeams()
    {
        Debug.Log("🎮 Setting up production teams with role assignment...");

        // Setup SquadMates (Team A)
        if (squadSpawns != null && squadmatePrefab != null)
        {
            SpawnTeam("SquadMate", squadmatePrefab, squadSpawns, ref squadMates, Color.blue);
            AssignTeammates(squadMates);
        }

        // Setup Enemies (Team B)
        if (enemySpawns != null && enemyBotPrefab != null)
        {
            SpawnTeam("EnemyBot", enemyBotPrefab, enemySpawns, ref enemies, Color.red);
            AssignTeammates(enemies);
        }

        Debug.Log("✅ Production teams setup complete");
    }

    void SpawnTeam(string tag, GameObject prefab, Transform[] spawns, ref GameObject[] teamArray, Color teamColor)
    {
        teamArray = new GameObject[spawns.Length];

        for (int i = 0; i < spawns.Length; i++)
        {
            GameObject bot = Instantiate(prefab, spawns[i].position, Quaternion.identity);
            bot.name = tag + "_" + (i + 1);
            bot.tag = tag;

            // Apply team color to renderer
            Renderer renderer = bot.GetComponent<Renderer>();
            if (renderer != null)
            {
                renderer.material.color = teamColor;
            }

            // Configure agent
            var agent = bot.GetComponent<SquadMateAgent>();
            if (agent != null)
            {
                agent.mapZones = mapZones;
                agent.squadLeader = spawns[0]; // fallback leader is spawn 0
            }

            // Configure VictorAgent if present
            var victorAgent = bot.GetComponent<VictorAgent>();
            if (victorAgent != null)
            {
                TeamID teamID = (tag == "SquadMate") ? TeamID.TeamA : TeamID.TeamB;
                victorAgent.Initialize(teamID, this);
            }

            teamArray[i] = bot;
        }
    }

    void AssignTeammates(GameObject[] team)
    {
        foreach (var bot in team)
        {
            var agent = bot.GetComponent<SquadMateAgent>();
            if (agent != null)
            {
                agent.squadLeader = team[0].transform;
            }
        }
    }

    VictorAgent CreateVictorAgent(string agentName, Vector3 position, TeamID team)
    {
        // Instantiate Victor prefab
        GameObject agentObj = Instantiate(victorAgentPrefab, position, Quaternion.identity);
        agentObj.name = agentName;

        // Get or add VictorAgent component
        VictorAgent victorAgent = agentObj.GetComponent<VictorAgent>();
        if (victorAgent == null)
        {
            victorAgent = agentObj.AddComponent<VictorAgent>();
        }

        // Configure agent for TDM
        victorAgent.Initialize(team, this);

        // Set team-specific materials/colors
        SetTeamVisuals(agentObj, team);

        return victorAgent;
    }

    void SetTeamVisuals(GameObject agent, TeamID team)
    {
        // Get renderer components
        Renderer[] renderers = agent.GetComponentsInChildren<Renderer>();

        foreach (Renderer renderer in renderers)
        {
            Material[] materials = renderer.materials;

            // Apply team colors to gear material (mat0.002_baseColor)
            for (int i = 0; i < materials.Length; i++)
            {
                if (materials[i].name.Contains("gear") || materials[i].name.Contains("mat0.002"))
                {
                    if (team == TeamID.TeamA)
                    {
                        materials[i].color = Color.blue; // Team A - Blue
                    }
                    else
                    {
                        materials[i].color = Color.red; // Team B - Red
                    }
                }
            }

            renderer.materials = materials;
        }
    }

    Vector3 GetSpawnPosition(Transform[] spawnPoints, int index)
    {
        if (spawnPoints.Length == 0)
        {
            Debug.LogWarning("⚠️ No spawn points available, using default position");
            return Vector3.zero;
        }

        // Use modulo to cycle through spawn points if we have more agents than points
        int spawnIndex = index % spawnPoints.Length;
        return spawnPoints[spawnIndex].position;
    }

    public void StartMatch()
    {
        Debug.Log("🚀 Starting 5v5 TDM Match!");

        matchActive = true;
        matchTimer = 0f;
        teamAScore = 0;
        teamBScore = 0;

        // Reset all agents
        foreach (VictorAgent agent in teamA)
        {
            agent.ResetForNewMatch();
        }

        foreach (VictorAgent agent in teamB)
        {
            agent.ResetForNewMatch();
        }

        // Start match timer
        StartCoroutine(MatchTimer());

        Debug.Log($"⏱️ Match started! Duration: {matchDuration}s, Score to win: {scoreToWin}");
    }

    IEnumerator MatchTimer()
    {
        while (matchActive && matchTimer < matchDuration)
        {
            matchTimer += Time.deltaTime;
            yield return null;
        }

        if (matchActive)
        {
            EndMatch("Time limit reached");
        }
    }

    public void OnAgentKilled(VictorAgent killedAgent, VictorAgent killerAgent)
    {
        if (!matchActive) return;

        // Award points to killer's team
        if (killerAgent != null)
        {
            if (killerAgent.teamID == TeamID.TeamA)
            {
                teamAScore++;
                Debug.Log($"🔵 Team A scored! Score: {teamAScore}-{teamBScore}");
            }
            else
            {
                teamBScore++;
                Debug.Log($"🔴 Team B scored! Score: {teamAScore}-{teamBScore}");
            }

            OnTeamScored?.Invoke((int)killerAgent.teamID);
        }

        // Check win condition
        if (teamAScore >= scoreToWin)
        {
            EndMatch("Team A reached score limit");
        }
        else if (teamBScore >= scoreToWin)
        {
            EndMatch("Team B reached score limit");
        }

        // Respawn killed agent after delay
        StartCoroutine(RespawnAgent(killedAgent, 3f));
    }

    IEnumerator RespawnAgent(VictorAgent agent, float delay)
    {
        yield return new WaitForSeconds(delay);

        if (matchActive && agent != null)
        {
            // Get appropriate spawn point
            Transform[] spawnPoints = (agent.teamID == TeamID.TeamA) ? teamASpawnPoints : teamBSpawnPoints;
            Vector3 spawnPos = GetSpawnPosition(spawnPoints, Random.Range(0, spawnPoints.Length));

            // Respawn agent
            agent.Respawn(spawnPos);
            Debug.Log($"♻️ {agent.name} respawned");
        }
    }

    void EndMatch(string reason)
    {
        if (!matchActive) return;

        matchActive = false;

        int winningTeam = (teamAScore > teamBScore) ? 0 : 1;
        Debug.Log($"🏆 Match ended: {reason}");
        Debug.Log($"📊 Final Score: Team A {teamAScore} - {teamBScore} Team B");
        Debug.Log($"🎉 Winner: Team {(winningTeam == 0 ? "A" : "B")}");

        OnMatchEnded?.Invoke(winningTeam);

        // Award final rewards to all agents
        AwardFinalRewards(winningTeam);

        // Start new match after delay
        StartCoroutine(StartNewMatchAfterDelay(5f));
    }

    void AwardFinalRewards(int winningTeam)
    {
        // Award rewards to winning team
        List<VictorAgent> winningAgents = (winningTeam == 0) ? teamA : teamB;
        List<VictorAgent> losingAgents = (winningTeam == 0) ? teamB : teamA;

        foreach (VictorAgent agent in winningAgents)
        {
            agent.AddReward(2f); // Big reward for winning
        }

        foreach (VictorAgent agent in losingAgents)
        {
            agent.AddReward(-0.5f); // Small penalty for losing
        }
    }

    IEnumerator StartNewMatchAfterDelay(float delay)
    {
        yield return new WaitForSeconds(delay);
        StartMatch();
    }

    void ClearTeams()
    {
        // Destroy existing agents
        foreach (GameObject agent in activeAgents)
        {
            if (agent != null)
            {
                DestroyImmediate(agent);
            }
        }

        activeAgents.Clear();
        teamA.Clear();
        teamB.Clear();
    }

    public List<VictorAgent> GetEnemyTeam(TeamID team)
    {
        return (team == TeamID.TeamA) ? teamB : teamA;
    }

    public List<VictorAgent> GetFriendlyTeam(TeamID team)
    {
        return (team == TeamID.TeamA) ? teamA : teamB;
    }

    public Transform GetNearestCaptureZone(Vector3 position)
    {
        if (captureZones.Length == 0) return null;

        Transform nearest = captureZones[0];
        float minDistance = Vector3.Distance(position, nearest.position);

        for (int i = 1; i < captureZones.Length; i++)
        {
            float distance = Vector3.Distance(position, captureZones[i].position);
            if (distance < minDistance)
            {
                minDistance = distance;
                nearest = captureZones[i];
            }
        }

        return nearest;
    }

    void OnGUI()
    {
        if (!matchActive) return;

        // Display match info
        GUI.Box(new Rect(10, 10, 200, 120), "");
        GUI.Label(new Rect(20, 20, 180, 20), $"🔵 Team A: {teamAScore}");
        GUI.Label(new Rect(20, 40, 180, 20), $"🔴 Team B: {teamBScore}");
        GUI.Label(new Rect(20, 60, 180, 20), $"⏱️ Time: {(matchDuration - matchTimer):F0}s");
        GUI.Label(new Rect(20, 80, 180, 20), $"🎯 Target: {scoreToWin}");
        GUI.Label(new Rect(20, 100, 180, 20), $"👥 Agents: {teamA.Count}v{teamB.Count}");
    }

    /// <summary>
    /// Get match statistics for training analysis
    /// </summary>
    public MatchStats GetMatchStats()
    {
        return new MatchStats
        {
            teamAScore = teamAScore,
            teamBScore = teamBScore,
            matchTime = matchTimer,
            teamAAgents = teamA.Count,
            teamBAgents = teamB.Count,
            isActive = matchActive
        };
    }
}

public enum TeamID
{
    TeamA = 0,
    TeamB = 1
}

[System.Serializable]
public struct MatchStats
{
    public int teamAScore;
    public int teamBScore;
    public float matchTime;
    public int teamAAgents;
    public int teamBAgents;
    public bool isActive;
}
