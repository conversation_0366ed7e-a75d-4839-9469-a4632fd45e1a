using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Flank Route System
/// Defines tactical flanking paths for AI agents
/// </summary>
[System.Serializable]
public class FlankRoute : MonoBehaviour
{
    [Header("🔄 Route Configuration")]
    public string routeName = "Flank Route";
    public RouteType routeType = RouteType.Flank;
    public float routeDifficulty = 1f;
    public float estimatedTime = 10f;

    [Header("📍 Route Points")]
    public List<Transform> waypoints = new List<Transform>();
    public Transform startPoint;
    public Transform endPoint;

    [Header("🎯 Tactical Properties")]
    public bool requiresStealth = false;
    public bool providesAdvantage = true;
    public float coverLevel = 0.5f;
    public float exposureRisk = 0.3f;

    [Header("🎨 Visual Settings")]
    public Color routeColor = Color.yellow;
    public bool showRoute = true;
    public float lineWidth = 0.1f;

    [Header("📊 Route Status")]
    public bool isActive = true;
    public bool isBlocked = false;
    public List<VictorAgent> agentsUsingRoute = new List<VictorAgent>();
    public float lastUsedTime = 0f;

    // Route management
    private LineRenderer lineRenderer;
    private float routeLength = 0f;
    private bool isInitialized = false;

    public enum RouteType
    {
        Flank,
        Retreat,
        Advance,
        Patrol,
        Escape,
        Ambush
    }

    void Start()
    {
        InitializeRoute();
        RegisterWithRouteManager();
    }

    void InitializeRoute()
    {
        // Create line renderer for visualization
        lineRenderer = GetComponent<LineRenderer>();
        if (lineRenderer == null)
        {
            lineRenderer = gameObject.AddComponent<LineRenderer>();
        }

        SetupLineRenderer();
        UpdateRouteVisualization();
        CalculateRouteLength();

        // Set default name if empty
        if (string.IsNullOrEmpty(routeName))
        {
            routeName = $"{routeType}_Route_{GetInstanceID()}";
        }

        gameObject.name = routeName;
        isInitialized = true;

        Debug.Log($"🔄 Flank route initialized: {routeName}");
    }

    void SetupLineRenderer()
    {
        lineRenderer.material = new Material(Shader.Find("Sprites/Default"));
        lineRenderer.color = routeColor;
        lineRenderer.startWidth = lineWidth;
        lineRenderer.endWidth = lineWidth;
        lineRenderer.useWorldSpace = true;
        lineRenderer.sortingOrder = 1;
    }

    void UpdateRouteVisualization()
    {
        if (lineRenderer == null || !showRoute) return;

        List<Vector3> routePoints = GetRoutePoints();
        lineRenderer.positionCount = routePoints.Count;

        for (int i = 0; i < routePoints.Count; i++)
        {
            lineRenderer.SetPosition(i, routePoints[i] + Vector3.up * 0.5f);
        }

        lineRenderer.enabled = showRoute && isActive;
    }

    List<Vector3> GetRoutePoints()
    {
        List<Vector3> points = new List<Vector3>();

        // Add start point
        if (startPoint != null)
        {
            points.Add(startPoint.position);
        }

        // Add waypoints
        foreach (Transform waypoint in waypoints)
        {
            if (waypoint != null)
            {
                points.Add(waypoint.position);
            }
        }

        // Add end point
        if (endPoint != null)
        {
            points.Add(endPoint.position);
        }

        return points;
    }

    void CalculateRouteLength()
    {
        routeLength = 0f;
        List<Vector3> points = GetRoutePoints();

        for (int i = 1; i < points.Count; i++)
        {
            routeLength += Vector3.Distance(points[i - 1], points[i]);
        }

        // Update estimated time based on route length
        estimatedTime = routeLength / 5f; // Assuming 5 units/second movement speed
    }

    void RegisterWithRouteManager()
    {
        FlankRouteManager routeManager = FindObjectOfType<FlankRouteManager>();
        if (routeManager != null)
        {
            routeManager.RegisterRoute(this);
        }
    }

    public bool CanUseRoute(VictorAgent agent)
    {
        if (!isActive || isBlocked) return false;

        // Check if route is already overcrowded
        if (agentsUsingRoute.Count >= GetMaxUsers()) return false;

        // Check if agent meets route requirements
        if (requiresStealth)
        {
            AgentRoleComponent roleComponent = agent.GetComponent<AgentRoleComponent>();
            if (roleComponent != null && roleComponent.currentRole != "Scout")
            {
                return false; // Only scouts can use stealth routes
            }
        }

        return true;
    }

    public void StartUsingRoute(VictorAgent agent)
    {
        if (!agentsUsingRoute.Contains(agent))
        {
            agentsUsingRoute.Add(agent);
            lastUsedTime = Time.time;

            Debug.Log($"🔄 {agent.name} started using {routeName}");
        }
    }

    public void StopUsingRoute(VictorAgent agent)
    {
        if (agentsUsingRoute.Contains(agent))
        {
            agentsUsingRoute.Remove(agent);

            Debug.Log($"🔄 {agent.name} finished using {routeName}");
        }
    }

    public Vector3 GetNextWaypoint(VictorAgent agent, Vector3 currentPosition)
    {
        List<Vector3> points = GetRoutePoints();
        if (points.Count == 0) return currentPosition;

        // Find the closest point on the route
        int closestIndex = 0;
        float closestDistance = float.MaxValue;

        for (int i = 0; i < points.Count; i++)
        {
            float distance = Vector3.Distance(currentPosition, points[i]);
            if (distance < closestDistance)
            {
                closestDistance = distance;
                closestIndex = i;
            }
        }

        // Return the next waypoint
        if (closestIndex < points.Count - 1)
        {
            return points[closestIndex + 1];
        }

        return points[points.Count - 1]; // Return end point
    }

    public float GetRouteProgress(VictorAgent agent, Vector3 currentPosition)
    {
        List<Vector3> points = GetRoutePoints();
        if (points.Count < 2) return 1f;

        float totalDistance = 0f;
        float currentDistance = 0f;

        // Calculate total route distance
        for (int i = 1; i < points.Count; i++)
        {
            totalDistance += Vector3.Distance(points[i - 1], points[i]);
        }

        // Find current progress
        int closestIndex = 0;
        float closestDist = float.MaxValue;

        for (int i = 0; i < points.Count; i++)
        {
            float dist = Vector3.Distance(currentPosition, points[i]);
            if (dist < closestDist)
            {
                closestDist = dist;
                closestIndex = i;
            }
        }

        // Calculate distance covered up to closest point
        for (int i = 1; i <= closestIndex; i++)
        {
            currentDistance += Vector3.Distance(points[i - 1], points[i]);
        }

        return Mathf.Clamp01(currentDistance / totalDistance);
    }

    public bool IsRouteComplete(VictorAgent agent, Vector3 currentPosition)
    {
        if (endPoint == null) return false;

        float distanceToEnd = Vector3.Distance(currentPosition, endPoint.position);
        return distanceToEnd <= 3f; // Within 3 units of end point
    }

    public float CalculateRouteScore(VictorAgent agent, Vector3 targetPosition)
    {
        float score = 1f;

        // Distance factor
        float distanceToStart = startPoint != null ?
            Vector3.Distance(agent.transform.position, startPoint.position) : 0f;
        float distanceToTarget = endPoint != null ?
            Vector3.Distance(endPoint.position, targetPosition) : float.MaxValue;

        score -= (distanceToStart + distanceToTarget) * 0.01f;

        // Route type bonuses
        AgentRoleComponent roleComponent = agent.GetComponent<AgentRoleComponent>();
        if (roleComponent != null)
        {
            switch (roleComponent.currentRole)
            {
                case "Scout":
                    if (routeType == RouteType.Flank || routeType == RouteType.Patrol)
                        score += 0.5f;
                    break;
                case "Assault":
                    if (routeType == RouteType.Advance || routeType == RouteType.Ambush)
                        score += 0.3f;
                    break;
                case "Support":
                    if (routeType == RouteType.Retreat || routeType == RouteType.Escape)
                        score += 0.4f;
                    break;
            }
        }

        // Tactical advantages
        if (providesAdvantage) score += 0.2f;
        score += coverLevel * 0.3f;
        score -= exposureRisk * 0.2f;

        // Usage penalty (avoid overcrowded routes)
        score -= agentsUsingRoute.Count * 0.1f;

        return score;
    }

    int GetMaxUsers()
    {
        switch (routeType)
        {
            case RouteType.Flank:
                return 2; // Small flanking groups
            case RouteType.Advance:
                return 3; // Medium assault groups
            case RouteType.Retreat:
                return 5; // Allow full team retreat
            default:
                return 2;
        }
    }

    public void SetBlocked(bool blocked)
    {
        isBlocked = blocked;

        if (blocked)
        {
            // Notify all agents using this route
            foreach (VictorAgent agent in agentsUsingRoute)
            {
                agent.OnRouteBlocked(this);
            }
        }

        UpdateRouteVisualization();
    }

    public void AddWaypoint(Vector3 position)
    {
        GameObject waypointObj = new GameObject($"Waypoint_{waypoints.Count}");
        waypointObj.transform.position = position;
        waypointObj.transform.SetParent(transform);

        waypoints.Add(waypointObj.transform);

        UpdateRouteVisualization();
        CalculateRouteLength();
    }

    public void RemoveWaypoint(int index)
    {
        if (index >= 0 && index < waypoints.Count)
        {
            if (waypoints[index] != null)
            {
                DestroyImmediate(waypoints[index].gameObject);
            }
            waypoints.RemoveAt(index);

            UpdateRouteVisualization();
            CalculateRouteLength();
        }
    }

    public void ReverseRoute()
    {
        // Swap start and end points
        Transform temp = startPoint;
        startPoint = endPoint;
        endPoint = temp;

        // Reverse waypoints
        waypoints.Reverse();

        UpdateRouteVisualization();
    }

    public FlankRoute CreateReverseRoute()
    {
        GameObject reverseRouteObj = new GameObject($"{routeName}_Reverse");
        FlankRoute reverseRoute = reverseRouteObj.AddComponent<FlankRoute>();

        reverseRoute.routeName = $"{routeName}_Reverse";
        reverseRoute.routeType = routeType;
        reverseRoute.startPoint = endPoint;
        reverseRoute.endPoint = startPoint;

        // Reverse waypoints
        for (int i = waypoints.Count - 1; i >= 0; i--)
        {
            reverseRoute.waypoints.Add(waypoints[i]);
        }

        reverseRoute.routeColor = routeColor;
        reverseRoute.requiresStealth = requiresStealth;
        reverseRoute.providesAdvantage = providesAdvantage;
        reverseRoute.coverLevel = coverLevel;
        reverseRoute.exposureRisk = exposureRisk;

        return reverseRoute;
    }

    public void DrawGizmos()
    {
        if (!showRoute) return;

        List<Vector3> points = GetRoutePoints();

        // Draw route line
        Gizmos.color = isBlocked ? Color.red : routeColor;
        for (int i = 1; i < points.Count; i++)
        {
            Gizmos.DrawLine(points[i - 1], points[i]);
        }

        // Draw waypoints
        Gizmos.color = Color.white;
        foreach (Vector3 point in points)
        {
            Gizmos.DrawWireSphere(point, 0.5f);
        }

        // Draw start and end markers
        if (startPoint != null)
        {
            Gizmos.color = Color.green;
            Gizmos.DrawSphere(startPoint.position, 0.8f);
        }

        if (endPoint != null)
        {
            Gizmos.color = Color.red;
            Gizmos.DrawSphere(endPoint.position, 0.8f);
        }
    }

    void OnValidate()
    {
        if (Application.isPlaying && isInitialized)
        {
            UpdateRouteVisualization();
            CalculateRouteLength();
        }
    }
}

/// <summary>
/// Flank Route Manager
/// Manages all flank routes in the scene
/// </summary>
public class FlankRouteManager : MonoBehaviour
{
    [Header("🔄 Route Management")]
    public List<FlankRoute> allRoutes = new List<FlankRoute>();
    public bool enableDynamicRouting = true;
    public float routeUpdateInterval = 5f;

    private static FlankRouteManager instance;

    void Awake()
    {
        if (instance == null)
        {
            instance = this;
        }
        else
        {
            Destroy(gameObject);
        }
    }

    void Start()
    {
        if (enableDynamicRouting)
        {
            InvokeRepeating(nameof(UpdateRoutes), routeUpdateInterval, routeUpdateInterval);
        }
    }

    public void RegisterRoute(FlankRoute route)
    {
        if (!allRoutes.Contains(route))
        {
            allRoutes.Add(route);
            Debug.Log($"🔄 Registered route: {route.routeName}");
        }
    }

    public void UnregisterRoute(FlankRoute route)
    {
        allRoutes.Remove(route);
    }

    public FlankRoute GetBestRoute(VictorAgent agent, Vector3 targetPosition, FlankRoute.RouteType preferredType = FlankRoute.RouteType.Flank)
    {
        FlankRoute bestRoute = null;
        float bestScore = float.MinValue;

        foreach (FlankRoute route in allRoutes)
        {
            if (route.CanUseRoute(agent) && route.routeType == preferredType)
            {
                float score = route.CalculateRouteScore(agent, targetPosition);
                if (score > bestScore)
                {
                    bestScore = score;
                    bestRoute = route;
                }
            }
        }

        return bestRoute;
    }

    void UpdateRoutes()
    {
        // Clean up null routes
        allRoutes.RemoveAll(route => route == null);

        // Update route status
        foreach (FlankRoute route in allRoutes)
        {
            // Check if route is blocked by enemies
            CheckRouteBlocked(route);
        }
    }

    void CheckRouteBlocked(FlankRoute route)
    {
        // Simple check - if enemies are near the route, mark as blocked
        VictorAgent[] allAgents = FindObjectsOfType<VictorAgent>();

        foreach (VictorAgent agent in allAgents)
        {
            if (agent.teamID != TeamID.TeamA) // Assuming we're checking for Team A
            {
                foreach (Transform waypoint in route.waypoints)
                {
                    if (waypoint != null)
                    {
                        float distance = Vector3.Distance(agent.transform.position, waypoint.position);
                        if (distance <= 5f) // Enemy within 5 units of waypoint
                        {
                            route.SetBlocked(true);
                            return;
                        }
                    }
                }
            }
        }

        // If no enemies found, unblock the route
        route.SetBlocked(false);
    }

    public static FlankRouteManager Instance => instance;
}
