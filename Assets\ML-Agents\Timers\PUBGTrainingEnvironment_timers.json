{"count": 1, "self": 6399.2147968, "total": 6399.4501169, "children": {"InitializeActuators": {"count": 1, "self": 0.0019946, "total": 0.0019946, "children": null}, "InitializeSensors": {"count": 1, "self": 0.0019952999999999998, "total": 0.0019952999999999998, "children": null}, "AgentSendState": {"count": 35487, "self": 0.056420399999999996, "total": 0.056420399999999996, "children": null}, "DecideAction": {"count": 35487, "self": 0.1520533, "total": 0.1520533, "children": null}, "AgentAct": {"count": 35487, "self": 0.0228138, "total": 0.0228138, "children": null}}, "gauges": {"SquadMate.CumulativeReward": {"count": 1, "max": 0, "min": 0, "runningAverage": 0, "value": 0, "weightedAverage": 0}}, "metadata": {"timer_format_version": "0.1.0", "start_time_seconds": "**********", "unity_version": "6000.1.6f1", "command_line_arguments": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.6f1\\Editor\\Unity.exe -projectpath C:\\Squadmate -useHub -hubIPC -cloudEnvironment production -licensingIpc LicenseClient-BCE -hubSessionId ff54f03a-f90d-4071-b7aa-091ab5af0106 -accessToken G7Vh0YgKJJmfLUc5v9gzepYv2-ktbQdv-8j0RpDE7Xo00cf", "communication_protocol_version": "1.5.0", "com.unity.ml-agents_version": "2.0.1", "scene_name": "PUBGTrainingEnvironment", "end_time_seconds": "**********"}}